package com.socialplay.gpark.ui.account.setting

import android.content.Context
import androidx.core.app.NotificationManagerCompat
import com.airbnb.mvrx.asMavericksArgs
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.interactor.UpdateAppInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.MineActionItem
import com.socialplay.gpark.data.model.MineActionJump
import com.socialplay.gpark.data.model.feedback.SubmitNewFeedbackRequest
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventConstantsWrapper
import com.socialplay.gpark.function.analytics.Source
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.feedback.FeedbackFragmentArgs
import org.koin.core.context.GlobalContext

object SettingItemConfigWrapper {
    fun getItemList(
        application: Context,
        h5PageConfigInteractor: H5PageConfigInteractor,
        metaKV: MetaKV
    ): java.util.ArrayList<MineActionItem> {

        val list = ArrayList<MineActionItem>()

        // 分割线
//        list.add(
//            MineActionItem(
//                0,
//                jump = MineActionJump.Space(4.dp)
//            )
//        )
        // 实名认证
        list.add(
            MineActionItem(
                R.string.setting_item_title_realname,
                jump = MineActionJump.RealName(
                    EventConstants.EVENT_REALNAME_NEED_CLICK,
                )
            )
        )

        // 隐私设置
        list.add(
            MineActionItem(
                R.string.privacy_settings,
                jump = MineActionJump.GraphNav(R.id.privacySetting),
                showLabelRedDot = metaKV.appKV.iShowScreenshotSettingRedHot
            )
        )
        // 账户设置
        list.add(
            MineActionItem(
                R.string.mine_item_account,
                jump = MineActionJump.AccountSettingActionItem(EventConstants.EVENT_MINE_ACCOUNT_CLICK)
            )
        )

        if (!NotificationManagerCompat.from(application).areNotificationsEnabled()) {
            // 通知权限设置
            list.add(
                MineActionItem(
                    R.string.notifications,
                    jump = MineActionJump.GraphNav(
                        R.id.notification_setting,
                        EventConstants.EVENT_NOTIFICATION_SETTING_CLICK
                    )
                )
            )

//            // 分割线
//            list.add(
//                MineActionItem(
//                    0,
//                    jump = MineActionJump.Space(4.dp)
//                )
//            )
        }

        // 意见反馈
        list.add(
            MineActionItem(
                R.string.feedback,
                jump = MineActionJump.GraphNav(
                    R.id.feedback,
                    EventConstants.EVENT_SETTING_FEEDBACK_CLICK,
                    FeedbackFragmentArgs(
                        null,
                        SubmitNewFeedbackRequest.SOURCE_APP_NUMBER,
                        null,
                        needBackRole = false,
                        needBackGame = false,
                        fromGameId = null
                    ).asMavericksArgs()
                )
            )
        )
        // 分割线
//        list.add(
//            MineActionItem(
//                0,
//                jump = MineActionJump.Space(4.dp)
//            )
//        )
        // 用户协议
        list.add(
            MineActionItem(
                R.string.terms_of_service_text,
                jump = MineActionJump.Url(
                    h5PageConfigInteractor.getH5PageUrl(H5PageConfigInteractor.USER_AGREEMENT),
                    EventConstants.EVENT_USER_AGREEMENT_CLICK,
                    Source.SOURCE_MINE,
                    useBabel = false,
                    useCompatParams = false
                )
            )
        )
        // 隐私协议
        list.add(
            MineActionItem(
                R.string.privacy_protocol,
                jump = MineActionJump.Url(
                    h5PageConfigInteractor.getH5PageUrl(H5PageConfigInteractor.PRIVACY_AGREEMENT),
                    EventConstants.EVENT_PRIVACY_AGREEMENT_CLICK,
                    Source.SOURCE_MINE,
                    useBabel = false,
                    useCompatParams = false
                )
            )
        )

        // 第三方SDK目录
        list.add(
            MineActionItem(
                R.string.setting_item_title_third_sdk_dir,
                jump = MineActionJump.Url(
                    h5PageConfigInteractor.getH5PageUrl(H5PageConfigInteractor.THIRD_SDK_LIST_PROTOCOL),
                    EventConstants.EVENT_THIRD_SDK_CLICK,
                    Source.SOURCE_MINE,
                    useBabel = false,
                    useCompatParams = false
                )
            )
        )

        // 群聊服务条款
        if (PandoraToggle.showCreateGroupMenu) {
            list.add(
                MineActionItem(
                    R.string.setting_item_title_group_chat_agreement,
                    jump = MineActionJump.Url(
                        h5PageConfigInteractor.getH5PageUrl(H5PageConfigInteractor.GROUP_CHAT_AGREEMENT),
                        EventConstants.EVENT_GROUP_CHAT_AGREEMENT_CLICK,
                        Source.SOURCE_MINE
                    )
                )
            )
        }

        // 侵权投诉指引
        list.add(
            MineActionItem(
                R.string.setting_item_title_complaining_infringement_guidelines,
                jump = MineActionJump.Url(
                    h5PageConfigInteractor.getH5PageUrl(H5PageConfigInteractor.COMPLAINING_INFRINGEMENT_GUIDELINES),
                    EventConstants.EVENT_COMPLAINING_INFRINGEMENT_CLICK,
                    Source.SOURCE_MINE
                )
            )
        )

        // 免责声明
        list.add(
            MineActionItem(
                R.string.setting_item_title_disclaimer,
                jump = MineActionJump.Url(
                    h5PageConfigInteractor.getH5PageUrl(H5PageConfigInteractor.DISCLAIMER_PROTOCOL),
                    EventConstants.EVENT_DISCLAIMER_CLICK,
                    Source.SOURCE_MINE
                )
            )
        )

        if (PandoraToggle.enableAssetCommercialization) {
            list.add(
                MineActionItem(
                    R.string.asset_comm_promo_content_4_guide_1,
                    jump = MineActionJump.Url(
                        url = h5PageConfigInteractor.getH5PageUrl(H5PageConfigInteractor.MOD_PUBLISH_STATEMENT),
                        event = EventConstants.ASSET_PUBLISHING_POLICY_CLICK,
                        source = Source.SOURCE_MINE,
                        needSource = false
                    ),
                )
            )
            list.add(
                MineActionItem(
                    R.string.asset_comm_promo_content_4_guide_2,
                    jump = MineActionJump.Url(
                        url = h5PageConfigInteractor.getH5PageUrl(H5PageConfigInteractor.MOD_PURCHASE_STATEMENT),
                        event = EventConstants.SETTING_ASSET_PURCHASING_CLICK,
                        source = Source.SOURCE_MINE,
                        needSource = false
                    ),
                )
            )
        }

        // 京ICP备2024065344号-5A
        list.add(
            MineActionItem(
                R.string.setting_item_title_filing_number,
                jump = MineActionJump.Url(
                    h5PageConfigInteractor.getH5PageUrl(H5PageConfigInteractor.WEB_URL_RECORD),
                    EventConstants.EVENT_FILING_NUMBER_CLICK,
                    Source.SOURCE_MINE,
                    useBabel = false,
                    useCompatParams = false
                )
            )
        )

        val updateAppInteractor: UpdateAppInteractor = GlobalContext.get().get()
        val updateInfo = updateAppInteractor.updateInfoLiveData.value
        // 检查更新
        list.add(
            MineActionItem(
                R.string.check_update,
                jump = MineActionJump.Update(EventConstantsWrapper.UPDATE_CHECK_CLICK),
                showLabelRedDot = updateInfo != null
            )
        )

        // 关于我们
        list.add(
            MineActionItem(
                R.string.about_us,
                jump = MineActionJump.GraphNav(R.id.about_us, EventConstants.EVENT_ABOUT_US_CLICK)
            )
        )

        // 退出登录
        list.add(
            MineActionItem(
                R.string.logout,
                jump = MineActionJump.LogoutActionItem(EventConstants.EVENT_LOGOUT_CLICK)
            )
        )
        return list
    }
}