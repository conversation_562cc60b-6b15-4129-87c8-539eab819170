package com.socialplay.gpark.util

import timber.log.Timber

/**
 * Glide异常处理器
 * 
 * 专门处理Glide相关的崩溃，特别是"Cannot obtain size for recycled Bitmap"异常
 */
class GlideExceptionHandler(private val defaultHandler: Thread.UncaughtExceptionHandler?) : Thread.UncaughtExceptionHandler {
    
    companion object {
        private const val TAG = "GlideExceptionHandler"
        
        /**
         * 安装Glide异常处理器
         */
        fun install() {
            val currentHandler = Thread.getDefaultUncaughtExceptionHandler()
            if (currentHandler !is GlideExceptionHandler) {
                Thread.setDefaultUncaughtExceptionHandler(GlideExceptionHandler(currentHandler))
                Timber.tag(TAG).d("Glide exception handler installed")
            }
        }
    }
    
    override fun uncaughtException(t: Thread, e: Throwable) {
        try {
            if (isGlideRecycledBitmapException(e)) {
                Timber.tag(TAG).w(e, "Caught Glide recycled bitmap exception, preventing crash")
                
                // 记录异常但不崩溃
                logGlideException(e)
                
                // 尝试清理Glide内存
                try {
                    GlideCrashFix.safeClearGlideMemory()
                } catch (cleanupException: Exception) {
                    Timber.tag(TAG).w(cleanupException, "Failed to cleanup Glide memory after exception")
                }
                
                return // 不调用默认处理器，防止崩溃
            }
        } catch (handlerException: Exception) {
            Timber.tag(TAG).e(handlerException, "Error in Glide exception handler")
        }
        
        // 对于非Glide异常，调用默认处理器
        defaultHandler?.uncaughtException(t, e)
    }
    
    /**
     * 检查是否是Glide回收Bitmap异常
     */
    private fun isGlideRecycledBitmapException(throwable: Throwable): Boolean {
        val message = throwable.message ?: ""
        val stackTrace = throwable.stackTraceToString()
        
        // 检查异常消息
        if (message.contains("Cannot obtain size for recycled Bitmap")) {
            return true
        }
        
        // 检查堆栈跟踪中是否包含Glide相关的类
        val glideClasses = listOf(
            "com.bumptech.glide.util.Util.getBitmapByteSize",
            "com.bumptech.glide.load.engine.bitmap_recycle.SizeConfigStrategy.removeLast",
            "com.bumptech.glide.load.engine.bitmap_recycle.LruBitmapPool.trimToSize",
            "com.bumptech.glide.load.engine.bitmap_recycle.LruBitmapPool.evict",
            "com.bumptech.glide.load.engine.bitmap_recycle.LruBitmapPool.put",
            "com.bumptech.glide.load.resource.bitmap.BitmapResource.recycle"
        )
        
        return glideClasses.any { stackTrace.contains(it) }
    }
    
    /**
     * 记录Glide异常的详细信息
     */
    private fun logGlideException(throwable: Throwable) {
        val message = buildString {
            appendLine("=== Glide Recycled Bitmap Exception Caught ===")
            appendLine("Exception: ${throwable.javaClass.simpleName}")
            appendLine("Message: ${throwable.message}")
            appendLine("Thread: ${Thread.currentThread().name}")
            appendLine("BitmapPool Info: ${GlideCrashFix.getBitmapPoolStatus()}")
            appendLine("Stack trace:")
            appendLine(throwable.stackTraceToString())
            appendLine("=== End of Glide Exception ===")
        }
        
        Timber.tag(TAG).w(message)
        
        // 可以在这里添加额外的日志记录或上报逻辑
        // 例如上报到崩溃分析平台
    }
}
