package com.socialplay.gpark.util

import android.graphics.Bitmap
import com.bumptech.glide.Glide
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * Glide崩溃修复工具类
 * 
 * 用于处理Glide在内存紧张时可能出现的"Cannot obtain size for recycled Bitmap"崩溃问题。
 * 
 * 主要修复措施：
 * 1. 统一使用BitmapRecycleUtil进行Bitmap回收
 * 2. 在Application.onTrimMemory中安全处理Glide内存清理
 * 3. 使用SafeLruBitmapPool替代默认的LruBitmapPool
 * 4. 所有直接调用bitmap.recycle()的地方都改为使用BitmapRecycleUtil.safeRecycle()
 */
object GlideCrashFix {
    
    private const val TAG = "GlideCrashFix"
    
    /**
     * 安全地清理Glide内存
     * 
     * @param level 内存清理级别
     */
    fun safeTrimGlideMemory(level: Int) {
        try {
            val context = GlobalContext.get().get<android.content.Context>()
            Glide.get(context).trimMemory(level)
            Timber.tag(TAG).d("Successfully trimmed Glide memory, level: $level")
        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "Failed to trim Glide memory safely, level: $level")
            // 如果trimMemory失败，尝试清理内存缓存
            safeClearGlideMemory()
        }
    }
    
    /**
     * 安全地清理Glide内存缓存
     */
    fun safeClearGlideMemory() {
        try {
            val context = GlobalContext.get().get<android.content.Context>()
            Glide.get(context).clearMemory()
            Timber.tag(TAG).d("Successfully cleared Glide memory cache")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Failed to clear Glide memory cache")
        }
    }
    
    /**
     * 检查Bitmap是否可以安全地放入Glide的BitmapPool
     * 
     * @param bitmap 要检查的Bitmap
     * @return 是否可以安全放入BitmapPool
     */
    fun canSafelyPutToBitmapPool(bitmap: Bitmap?): Boolean {
        if (bitmap == null || bitmap.isRecycled) {
            return false
        }
        
        // 检查Bitmap大小是否合理
        val width = bitmap.width
        val height = bitmap.height
        if (width <= 0 || height <= 0) {
            return false
        }
        
        // 检查Bitmap是否过大
        val maxSize = 4096 // 最大尺寸限制
        if (width > maxSize || height > maxSize) {
            Timber.tag(TAG).w("Bitmap too large for BitmapPool: ${width}x${height}")
            return false
        }
        
        return true
    }
    
    /**
     * 安全地将Bitmap放入Glide的BitmapPool
     * 
     * @param bitmap 要放入的Bitmap
     * @return 是否成功放入
     */
    fun safePutToBitmapPool(bitmap: Bitmap?): Boolean {
        if (!canSafelyPutToBitmapPool(bitmap)) {
            return false
        }
        
        return try {
            val context = GlobalContext.get().get<android.content.Context>()
            val bitmapPool = Glide.get(context).bitmapPool
            bitmapPool.put(bitmap)
            Timber.tag(TAG).d("Successfully put bitmap to BitmapPool: ${bitmap!!.width}x${bitmap.height}")
            true
        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "Failed to put bitmap to BitmapPool")
            false
        }
    }
    
    /**
     * 获取Glide BitmapPool的状态信息
     * 
     * @return BitmapPool状态信息
     */
    fun getBitmapPoolStatus(): String {
        return try {
            val context = GlobalContext.get().get<android.content.Context>()
            val bitmapPool = Glide.get(context).bitmapPool
            "BitmapPool - MaxSize: ${bitmapPool.maxSize / 1024 / 1024}MB, CurrentSize: ${bitmapPool.currentSize / 1024 / 1024}MB"
        } catch (e: Exception) {
            "BitmapPool status unavailable: ${e.message}"
        }
    }
}
