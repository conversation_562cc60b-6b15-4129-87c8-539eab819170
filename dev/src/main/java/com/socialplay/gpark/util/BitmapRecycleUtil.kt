package com.socialplay.gpark.util

import android.graphics.Bitmap
import com.bumptech.glide.Glide
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * Bitmap回收工具类
 * 
 * 统一使用Glide的BitmapPool来回收Bitmap，避免与Glide的内存管理产生冲突。
 * 这样可以确保所有Bitmap的回收都通过同一个机制进行，避免重复回收导致的崩溃。
 */
object BitmapRecycleUtil {

    /**
     * 安全回收Bitmap，优先使用Glide的BitmapPool
     *
     * @param bitmap 要回收的Bitmap
     * @param fallbackToDirectRecycle 如果Glide不可用，是否使用直接回收作为备选方案
     */
    fun safeRecycle(bitmap: Bitmap?, fallbackToDirectRecycle: Boolean = true) {
        if (bitmap == null || bitmap.isRecycled) {
            return
        }

        // 双重检查，确保Bitmap在回收过程中没有被其他地方回收
        synchronized(bitmap) {
            if (bitmap.isRecycled) {
                Timber.d("Bitmap already recycled, skip")
                return
            }

            try {
                // 使用GlideCrashFix安全地将Bitmap放入BitmapPool
                if (GlideCrashFix.safePutToBitmapPool(bitmap)) {
                    Timber.d("Bitmap recycled via Glide BitmapPool")
                    return
                }
            } catch (e: Exception) {
                Timber.w(e, "Failed to recycle bitmap via Glide BitmapPool")
            }

            if (fallbackToDirectRecycle && !bitmap.isRecycled) {
                // 如果Glide不可用，使用传统方式回收
                try {
                    bitmap.recycle()
                    Timber.d("Bitmap recycled via direct recycle()")
                } catch (recycleException: Exception) {
                    Timber.e(recycleException, "Failed to recycle bitmap via direct recycle()")
                }
            }
        }
    }

    /**
     * 批量回收Bitmap列表
     * 
     * @param bitmaps Bitmap列表
     * @param fallbackToDirectRecycle 如果Glide不可用，是否使用直接回收作为备选方案
     */
    fun safeRecycleAll(bitmaps: List<Bitmap?>, fallbackToDirectRecycle: Boolean = true) {
        bitmaps.forEach { bitmap ->
            safeRecycle(bitmap, fallbackToDirectRecycle)
        }
    }

    /**
     * 检查Bitmap是否可以安全回收
     * 
     * @param bitmap 要检查的Bitmap
     * @return 是否可以安全回收
     */
    fun canSafelyRecycle(bitmap: Bitmap?): Boolean {
        return bitmap != null && !bitmap.isRecycled
    }
} 