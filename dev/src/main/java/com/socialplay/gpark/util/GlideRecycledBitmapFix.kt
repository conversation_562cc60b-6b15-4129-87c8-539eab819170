package com.socialplay.gpark.util

import android.graphics.Bitmap
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.lang.reflect.Field
import java.lang.reflect.Method

/**
 * Glide回收Bitmap崩溃修复工具
 * 
 * 专门处理"Cannot obtain size for recycled Bitmap"崩溃问题
 * 这个问题通常发生在Glide内部资源回收过程中
 */
object GlideRecycledBitmapFix {
    
    private const val TAG = "GlideRecycledBitmapFix"
    private var isFixApplied = false
    
    /**
     * 应用修复，替换Glide的BitmapPool为安全版本
     */
    fun applyFix() {
        if (isFixApplied) {
            Timber.tag(TAG).d("Fix already applied")
            return
        }
        
        try {
            val context = GlobalContext.get().get<android.content.Context>()
            val glide = Glide.get(context)
            
            // 获取当前的BitmapPool
            val currentPool = glide.bitmapPool
            
            // 如果当前池不是我们的安全包装器，则包装它
            if (currentPool !is SafeBitmapPoolProxy) {
                val safePool = SafeBitmapPoolProxy(currentPool)
                
                // 使用反射替换BitmapPool
                replaceBitmapPool(glide, safePool)
                
                isFixApplied = true
                Timber.tag(TAG).d("Successfully applied Glide recycled bitmap fix")
            }
            
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Failed to apply Glide recycled bitmap fix")
        }
    }
    
    /**
     * 使用反射替换Glide的BitmapPool
     */
    private fun replaceBitmapPool(glide: Glide, newPool: BitmapPool) {
        try {
            // 尝试通过反射访问Glide的bitmapPool字段
            val glideClass = glide.javaClass
            val bitmapPoolField = findBitmapPoolField(glideClass)
            
            if (bitmapPoolField != null) {
                bitmapPoolField.isAccessible = true
                bitmapPoolField.set(glide, newPool)
                Timber.tag(TAG).d("Successfully replaced BitmapPool via reflection")
            } else {
                Timber.tag(TAG).w("Could not find BitmapPool field in Glide class")
            }
            
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Failed to replace BitmapPool via reflection")
        }
    }
    
    /**
     * 查找Glide类中的BitmapPool字段
     */
    private fun findBitmapPoolField(clazz: Class<*>): Field? {
        try {
            // 尝试常见的字段名
            val fieldNames = listOf("bitmapPool", "mBitmapPool", "pool")
            
            for (fieldName in fieldNames) {
                try {
                    val field = clazz.getDeclaredField(fieldName)
                    if (BitmapPool::class.java.isAssignableFrom(field.type)) {
                        return field
                    }
                } catch (e: NoSuchFieldException) {
                    // 继续尝试下一个字段名
                }
            }
            
            // 如果直接查找失败，遍历所有字段
            for (field in clazz.declaredFields) {
                if (BitmapPool::class.java.isAssignableFrom(field.type)) {
                    return field
                }
            }
            
            // 检查父类
            val superClass = clazz.superclass
            if (superClass != null && superClass != Any::class.java) {
                return findBitmapPoolField(superClass)
            }
            
        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "Error while searching for BitmapPool field")
        }
        
        return null
    }
    
    /**
     * 安全的BitmapPool代理，防止recycled bitmap异常
     */
    private class SafeBitmapPoolProxy(private val delegate: BitmapPool) : BitmapPool {
        
        override fun put(bitmap: Bitmap?) {
            if (bitmap == null || bitmap.isRecycled) {
                return
            }
            
            try {
                // 在put操作前后都检查bitmap状态
                if (!bitmap.isRecycled) {
                    delegate.put(bitmap)
                }
            } catch (e: IllegalStateException) {
                if (e.message?.contains("recycled Bitmap") == true) {
                    Timber.tag(TAG).d("Prevented recycled bitmap crash in put()")
                } else {
                    throw e
                }
            }
        }
        
        override fun get(width: Int, height: Int, config: Bitmap.Config): Bitmap {
            return try {
                delegate.get(width, height, config)
            } catch (e: Exception) {
                Timber.tag(TAG).w(e, "Error in get(), creating new bitmap")
                Bitmap.createBitmap(width, height, config)
            }
        }
        
        override fun getDirty(width: Int, height: Int, config: Bitmap.Config): Bitmap {
            return try {
                delegate.getDirty(width, height, config)
            } catch (e: Exception) {
                Timber.tag(TAG).w(e, "Error in getDirty(), creating new bitmap")
                Bitmap.createBitmap(width, height, config)
            }
        }
        
        override fun clearMemory() {
            try {
                delegate.clearMemory()
            } catch (e: IllegalStateException) {
                if (e.message?.contains("recycled Bitmap") == true) {
                    Timber.tag(TAG).d("Prevented recycled bitmap crash in clearMemory()")
                } else {
                    Timber.tag(TAG).w(e, "Error in clearMemory()")
                }
            } catch (e: Exception) {
                Timber.tag(TAG).w(e, "Unexpected error in clearMemory()")
            }
        }
        
        override fun trimMemory(level: Int) {
            try {
                delegate.trimMemory(level)
            } catch (e: IllegalStateException) {
                if (e.message?.contains("recycled Bitmap") == true) {
                    Timber.tag(TAG).d("Prevented recycled bitmap crash in trimMemory(), clearing pool instead")
                    try {
                        delegate.clearMemory()
                    } catch (clearException: Exception) {
                        Timber.tag(TAG).w(clearException, "Error clearing memory after trimMemory failure")
                    }
                } else {
                    Timber.tag(TAG).w(e, "Error in trimMemory()")
                }
            } catch (e: Exception) {
                Timber.tag(TAG).w(e, "Unexpected error in trimMemory()")
            }
        }
        
        override fun getMaxSize(): Long {
            return try {
                delegate.maxSize
            } catch (e: Exception) {
                Timber.tag(TAG).w(e, "Error getting max size")
                0L
            }
        }
        
        override fun setSizeMultiplier(sizeMultiplier: Float) {
            try {
                delegate.setSizeMultiplier(sizeMultiplier)
            } catch (e: Exception) {
                Timber.tag(TAG).w(e, "Error setting size multiplier")
            }
        }
    }
    
    /**
     * 检查修复是否已应用
     */
    fun isFixApplied(): Boolean = isFixApplied
    
    /**
     * 获取当前BitmapPool的状态信息
     */
    fun getBitmapPoolInfo(): String {
        return try {
            val context = GlobalContext.get().get<android.content.Context>()
            val glide = Glide.get(context)
            val pool = glide.bitmapPool
            val isProxy = pool is SafeBitmapPoolProxy
            val maxSizeMB = pool.maxSize / (1024 * 1024)
            "BitmapPool: ${pool.javaClass.simpleName}, MaxSize: ${maxSizeMB}MB, IsProxy: $isProxy"
        } catch (e: Exception) {
            "BitmapPool info unavailable: ${e.message}"
        }
    }
}
