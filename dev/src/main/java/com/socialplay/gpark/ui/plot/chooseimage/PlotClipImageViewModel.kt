package com.socialplay.gpark.ui.plot.chooseimage

import android.content.ComponentCallbacks
import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.ApiResultCodeException
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.interactor.UploadFileInteractor
import com.socialplay.gpark.data.model.im.RiskQueryResult
import com.socialplay.gpark.data.model.im.request.RiskImgCheckRequest
import com.socialplay.gpark.function.download.DownloadFileProvider
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.util.BitmapRecycleUtil
import com.socialplay.gpark.util.FileUtil
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.WebUtil
import com.socialplay.gpark.util.bitmap.BitmapUtil
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.singleOrNull
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get
import timber.log.Timber
import java.io.File

/**
 * Created by bo.li
 * Date: 2024/5/14
 * Desc:
 */
data class PlotClipImageModelState(
    val inputPath: String,
    val ratioWidth: Int,
    val ratioHeight: Int,
    val useClip: Boolean,
    val originBitmap: Bitmap? = null,
    // 审核+上传结果
    val result: Async<String> = Uninitialized,
    val cachePath: Uri ? = null,
) : MavericksState {
    constructor(args: PlotClipImageFragmentArgs) : this(
        args.inputPath,
        args.ratioWidth,
        args.ratioHeight,
        args.useClip,
    )
}

class PlotClipImageViewModel(
    private val repository: IMetaRepository,
    private val uploadFileInteractor: UploadFileInteractor,
    private val context: Context,
    initialState: PlotClipImageModelState
) : BaseViewModel<PlotClipImageModelState>(initialState) {

    private var processJob: Job? = null
    // 轮询策略
    private val pollingStrategy: List<AIGCPollingStrategy> = AIGCPollingStrategy.getDefault()

    companion object : KoinViewModelFactory<PlotClipImageViewModel, PlotClipImageModelState>() {
        // 错误次数上限
        private const val ERROR_LIMIT = 3
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: PlotClipImageModelState
        ): PlotClipImageViewModel {
            return PlotClipImageViewModel(get(), get(), get(), state)
        }
    }

    init {
        initBitmap()
        initImageSavePath(context)
    }

    private fun initBitmap() {
        viewModelScope.launch(Dispatchers.IO) {
            val path = awaitState().inputPath
            if (WebUtil.isHttpOrHttpsScheme(path)) {
                //网络图片
                Glide.with(context).asBitmap().load(path).into(object : SimpleTarget<Bitmap?>() {
                    override fun onResourceReady(
                        resource: Bitmap,
                        transition: Transition<in Bitmap?>?
                    ) {
                        setState {
                            copy(originBitmap = resource)
                        }
                    }
                })
            } else {
                val bitmap =
                    BitmapUtil.compressBitmapForWidth(
                        path,
                        ScreenUtil.getScreenWidth(context)
                    )
                setState {
                    copy(originBitmap = bitmap)
                }
            }
        }
    }

    /**
     * 选择图片时用了压缩引擎，此处不用再压缩了
     */
    fun processClip(filePath: String, bizCode: String, needCheck: Boolean) {
        if (oldState.result is Loading || oldState.result is Success) return
        processJob = suspend {
            val file = File(filePath)
            Timber.d("check_clip, no clip size:${FileUtil.getFileSize(file)}")
            if (!file.exists()) {
                throw ApiResultCodeException(
                    -1,
                    context.getString(R.string.failed_to_upload),
                    String::class
                )
            }
            val url = uploadImage(file, bizCode, needCheck)
            if (needCheck) {
                riskReview(url, RiskImgCheckRequest.AUTH_CODE_OC_SHORT_BACKGROUND)
            }
            url
        }.execute(Dispatchers.IO) {
            copy(result = it)
        }
    }

    /**
     * bizCode 上传的空间
     * needCheck 是否需要检查通过
     */
    fun processClip(bitmap: Bitmap, bizCode: String, needCheck: Boolean) {
        if (oldState.result is Loading || oldState.result is Success) return
        processJob = suspend {
            val cacheFile = getCachePhotoPath("clip_${System.currentTimeMillis()}.jpeg")
            BitmapUtil.saveToLocal(bitmap, cacheFile, 90)
            Timber.d("check_clip, clip size:${FileUtil.getFileSize(cacheFile)}")
            kotlin.runCatching { BitmapRecycleUtil.safeRecycle(bitmap) }
            val url = uploadImage(cacheFile, bizCode, needCheck)
            if (needCheck) {
                riskReview(url, RiskImgCheckRequest.AUTH_CODE_OC_SHORT_BACKGROUND)
            }
            url
        }.execute(Dispatchers.IO) {
            copy(result = it)
        }
    }

    @Throws
    private suspend fun uploadImage(file: File, bizCode: String, needCheck: Boolean): String {
        val result = uploadFileInteractor.uploadSingle(bizCode, file, true).singleOrNull()
        val url = result?.data?.url
        if (url.isNullOrEmpty()) {
            throw ApiResultCodeException(
                result?.code ?: -1,
                context.getString(R.string.failed_to_upload),
                String::class
            )
        } else {
            return url
        }
    }

    @Throws
    private suspend fun riskReview(url: String, authCode: String) {
        val createResult =
            repository.commonImageRiskCheck(url, authCode)
                .invoke()
        val taskId = createResult.jobId ?: throw ApiResultCodeException(
            -1,
            context.getString(R.string.common_error),
            String::class
        )
        var queryResult: RiskQueryResult?
        var nextWaitTime: Long
        var errorTimes = 0
        var times = 0
        while (true) {
            queryResult = runCatching {
                repository.queryImageRiskTask(taskId).invoke()
            }.getOrElse {
                errorTimes++
                Timber.e(it)
                null
            }
            if (queryResult == null || !queryResult.result) {
                // 还没出结果/接口不通
                times++
                nextWaitTime = nextWaitTime(errorTimes, times, pollingStrategy)
                if (nextWaitTime < 0) {
                    break
                }
                delay(nextWaitTime)
            } else if (queryResult.items?.firstOrNull() == null) {
                // 没有结果
                throw ApiResultCodeException(
                    -1,
                    context.getString(R.string.common_error),
                    String::class
                )
            } else if (queryResult.items?.firstOrNull()?.checkPass() != true) {
                // 没通过
                throw ApiResultCodeException(
                    -1,
                    context.getString(R.string.review_failed),
                    String::class
                )
            } else {
                break
            }
        }
    }

    /**
     * @return < 0 则结束
     */
    private fun nextWaitTime(errorTimes: Int, currentTimes: Int, strategyList: List<AIGCPollingStrategy>): Long {
        if (reachErrorLimit(errorTimes)) {
            Timber.d("nextWaitTime reachErrorLimit")
            return -1
        }
        var pollingLevel = 0
        strategyList.forEach {
            pollingLevel += it.pollingTimes.coerceAtLeast(1)
            if (currentTimes < pollingLevel) {
                Timber.d("nextWaitTime: ${it.waitTime}")
                return it.waitTime
            }
        }
        Timber.d("nextWaitTime noMore")
        return -1
    }

    private fun reachErrorLimit(errorTimes: Int): Boolean {
        return errorTimes >= ERROR_LIMIT
    }

    private fun getCachePhotoPath(name: String): File {
        return File("${DownloadFileProvider.chooseImgCacheDir}/${name}")
    }

    fun stopProcess() {
        processJob?.cancel()
        processJob = null
        setState {
            copy(result = Fail(CancellationException(context.getString(R.string.cancel_by_user))))
        }
    }
    private fun initImageSavePath(context: Context) = viewModelScope.launch(Dispatchers.IO) {
        val imageFile = File(context.cacheDir, "profile_avatar_${System.currentTimeMillis()}.jpg")
        val imageUri = Uri.fromFile(imageFile)
        setState { copy(cachePath = imageUri) }
    }

    fun deleteCache(uri: Uri) {
        GlobalScope.launch(Dispatchers.IO) {
            FileUtil.deleteFile(File(uri.path))
        }
    }
}