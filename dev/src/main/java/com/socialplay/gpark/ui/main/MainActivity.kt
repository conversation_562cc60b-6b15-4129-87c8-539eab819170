package com.socialplay.gpark.ui.main

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.os.SystemClock
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.core.view.LayoutInflaterCompat
import androidx.drawerlayout.widget.DrawerLayout
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavController
import androidx.navigation.NavHostController
import androidx.navigation.findNavController
import androidx.navigation.fragment.NavHostFragment
import com.airbnb.epoxy.EpoxyController
import androidx.navigation.plusAssign
import com.ly123.tes.mgs.metacloud.helper.CommandMessageRegistry
import com.meta.lib.mwbiz.MWBiz
import com.meta.lib.mwbiz.MWLifeCallback
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.BaseAccountInteractor
import com.socialplay.gpark.data.interactor.EditorGameLoadInteractor
import com.socialplay.gpark.data.interactor.FloatNoticeInteractor
import com.socialplay.gpark.data.interactor.QrCodeInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TimeKV.Companion.DAY_OPEN_APP
import com.socialplay.gpark.data.model.LoginSource
import com.socialplay.gpark.data.model.event.TokenInvalidEvent
import com.socialplay.gpark.data.model.mgs.CmdViolateBanMessage
import com.socialplay.gpark.data.model.mgs.CmdViolateWarnMessage
import com.socialplay.gpark.data.model.qrcode.ScanRequestData
import com.socialplay.gpark.data.model.qrcode.ScanResultData
import com.socialplay.gpark.databinding.ActivityMianBinding
import com.socialplay.gpark.function.ad.event.BackToGameEvent
import com.socialplay.gpark.function.ad.event.ProcessSwitchEvent
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.handle.AppLaunchAnalytics
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.apm.page.view.PageMonitorLayoutInflaterFactory2
import com.socialplay.gpark.function.deeplink.DeeplinkAnalysisUtil
import com.socialplay.gpark.function.deeplink.DispatchMgsShare
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.deeplink.MetaDeepLink.KEY_COMMAND
import com.socialplay.gpark.function.deeplink.MetaDeepLink.KEY_COMMON_BACK
import com.socialplay.gpark.function.deeplink.MetaDeepLink.PARAM_SOURCE_FROM
import com.socialplay.gpark.function.deeplink.ShareActiveHelper
import com.socialplay.gpark.function.editor.EditorGameInteractHelper
import com.socialplay.gpark.function.http.CheckTokenInterceptor
import com.socialplay.gpark.function.ipc.provider.host.HostIntentStarter
import com.socialplay.gpark.function.locale.MetaLanguageListener
import com.socialplay.gpark.function.locale.MetaLanguages
import com.socialplay.gpark.function.navigation.FragmentNavigatorPlus
import com.socialplay.gpark.function.navigation.KeepViewNavigator
import com.socialplay.gpark.function.navigation.NavigationGraphExt
import com.socialplay.gpark.function.overseabridge.bridge.DynamicLinksWrapper
import com.socialplay.gpark.function.overseabridge.bridge.IAdSdkBridge
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseActivity
import com.socialplay.gpark.ui.gamedetail.cache.GameDetailBindingCache
import com.socialplay.gpark.ui.notice.message.ImNotificationHandler
import com.socialplay.gpark.ui.qrcode.QRCodeScanFragment
import com.socialplay.gpark.ui.suggestion.GameSuggestionViewModel
import com.socialplay.gpark.ui.view.MetaTextView
import com.socialplay.gpark.util.QuitAppUtil
import com.socialplay.gpark.util.StatusBarUtil
import com.socialplay.gpark.util.StorageUtils
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.findTyped
import com.socialplay.gpark.util.extension.isPad
import com.socialplay.gpark.util.extension.toMap
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.util.Locale
import java.util.concurrent.atomic.AtomicBoolean


/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/09
 * desc   :
 * </pre>
 */


class MainActivity : BaseActivity(), MetaLanguageListener {

    private var loggingOut: AtomicBoolean = AtomicBoolean(false)
    override val binding by viewBinding(ActivityMianBinding::inflate)
    private val mainViewModel: MainViewModel by viewModel()
    private val gameSuggestionViewModel: GameSuggestionViewModel by viewModel()

    private val metaKV: MetaKV by inject()

    // 记录当前Activity是否在前台
    private var isFront: AtomicBoolean = AtomicBoolean(false)

    private val editorGameLoadInteractor by inject<EditorGameLoadInteractor>()

    private val backPressIdList by lazy {
        mutableSetOf(
            R.id.login,
            R.id.chatSetting,
            R.id.startupCreateAvatarV2,
            R.id.guideLogin,
            R.id.login_by_phone,
            R.id.bind_account_and_password,
            R.id.acc_pwd_v7_fragment,
            R.id.editProfile,
            R.id.reviewList,
            R.id.edit_game_review,
            R.id.post_detail,
            R.id.feedback,
        )
    }

    val drawerLayout: DrawerLayout
        get() = binding.root

    /**
     * 更准确地判断是否真的在首页
     * 结合Navigation状态和FragmentManager状态进行判断
     */
    private fun isReallyOnMainPage(navController: NavController): Boolean {
        val curDestinationId = navController.currentBackStackEntry?.destination?.id
        val preDestinationId = navController.previousBackStackEntry?.destination?.id

        // 基本的Navigation状态判断
        val navSaysMainPage = curDestinationId == R.id.main && (preDestinationId == null || preDestinationId == R.id.splash)

        // 检查FragmentManager的实际状态
        val navHostFragment = findNavHostFragment()
        if (navHostFragment != null) {
            val fragmentManager = navHostFragment.childFragmentManager
            val primaryFragment = fragmentManager.primaryNavigationFragment
            val backStackCount = fragmentManager.backStackEntryCount

            // 检查主导航Fragment是否真的是MainFragment
            val fragmentSaysMainPage = primaryFragment?.javaClass?.simpleName?.contains("MainFragment") == true

            if (BuildConfig.DEBUG) {
                Timber.tag("FragmentNavigatorPlus").d("isReallyOnMainPage: navSaysMainPage=$navSaysMainPage, fragmentSaysMainPage=$fragmentSaysMainPage, backStackCount=$backStackCount")
            }

            // 如果Navigation状态和Fragment状态不一致，以Fragment状态为准
            if (navSaysMainPage && !fragmentSaysMainPage && backStackCount > 1) {
                if (BuildConfig.DEBUG) {
                    Timber.tag("FragmentNavigatorPlus").w("isReallyOnMainPage: 检测到状态不一致，Navigation认为在首页但Fragment不是，以Fragment状态为准")
                }
                return false
            }

            // 如果回退栈中有多个条目，且当前不是MainFragment，则不在首页
            if (backStackCount > 1 && !fragmentSaysMainPage) {
                return false
            }

            return fragmentSaysMainPage
        }

        // 如果无法获取FragmentManager，回退到基本判断
        return navSaysMainPage
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        if (StorageUtils.isFullSDWhenStart) needSetContent = false
        AppLaunchAnalytics.mainStartTime = SystemClock.elapsedRealtime()
        setTextViewFactory2()
        super.onCreate(savedInstanceState)
        MetaLanguages.observeCallback(this, this)
        if (StorageUtils.isFullSDWhenStart) return

        window.decorView.setBackgroundColor(Color.WHITE)

        initEpoxyExceptionHandler()
        GameDetailBindingCache.getInstance().initPreload(this)
        StatusBarUtil.setTransparent(this)
        EventBus.getDefault().register(this)
        // 不同Flavor处理不同的页面id逻辑
        backPressIdList.addAll(MainBackPress.fetchBackPressIds())
        val navHostController = navController() as NavHostController
        NavigationGraphExt.printCNavigateChange(navHostController)
        val navHostFragment = findNavHostFragment()
        if (navHostFragment == null) {
            finish()
            return
        }
        val keepViewNavigator = FragmentNavigatorPlus(
            this,
            navHostFragment.childFragmentManager,
            R.id.nav_host_fragment
        )
        navHostController.navigatorProvider += keepViewNavigator
        navHostController.setGraph(R.navigation.root)
        navHostController.let {
            val callback = object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    val curDestinationId = it.currentBackStackEntry?.destination?.id
                    val preDestinationId = it.previousBackStackEntry?.destination?.id

                    if (BuildConfig.DEBUG) {
                        // 添加调试日志
                        Timber.tag("FragmentNavigatorPlus").d("MainActivity handleOnBackPressed: curDestinationId=$curDestinationId, preDestinationId=$preDestinationId")
                        Timber.tag("FragmentNavigatorPlus").d("MainActivity handleOnBackPressed: currentEntry=${it.currentBackStackEntry?.destination?.displayName}")
                        Timber.tag("FragmentNavigatorPlus").d("MainActivity handleOnBackPressed: previousEntry=${it.previousBackStackEntry?.destination?.displayName}")

                        // 额外检查：验证FragmentManager的状态
                        val navHostFragment = findNavHostFragment()
                        if (navHostFragment != null) {
                            val fragmentManager = navHostFragment.childFragmentManager
                            val activeFragments = fragmentManager.fragments
                            val primaryFragment = fragmentManager.primaryNavigationFragment

                            Timber.tag("FragmentNavigatorPlus").d("MainActivity handleOnBackPressed: 活跃Fragment数量=${activeFragments.size}")
                            Timber.tag("FragmentNavigatorPlus").d("MainActivity handleOnBackPressed: 主导航Fragment=${primaryFragment?.javaClass?.simpleName}")
                            Timber.tag("FragmentNavigatorPlus").d("MainActivity handleOnBackPressed: FragmentManager回退栈数量=${fragmentManager.backStackEntryCount}")

                            // 检查是否真的在首页
                            val isReallyOnMainPage = primaryFragment?.javaClass?.simpleName?.contains("MainFragment") == true
                            if (curDestinationId == R.id.main && !isReallyOnMainPage) {
                                Timber.tag("FragmentNavigatorPlus").w("MainActivity handleOnBackPressed: Navigation状态显示在首页，但FragmentManager状态不一致！")
                            }
                        }
                    }

                    // 增强的首页判断逻辑
                    val isOnMainPage = isReallyOnMainPage(it)

                    when {
                        isOnMainPage -> {
                            // 在首页
                            if (BuildConfig.DEBUG) {
                                Timber.tag("FragmentNavigatorPlus").d("MainActivity handleOnBackPressed: 在首页，显示退出确认")
                            }
                            QuitAppUtil.checkClickBackPressed(this@MainActivity) {
                                editorGameLoadInteractor.stop("quit app")
                            }
                        }

                        curDestinationId == null -> {
                            // 当前页面为null，跳转到首页
                            if (BuildConfig.DEBUG) {
                                Timber.tag("FragmentNavigatorPlus").d("MainActivity handleOnBackPressed: curDestinationId为null，跳转到首页")
                            }
                            MetaRouter.Main.tabHome(this@MainActivity)
                        }

                        preDestinationId == null && curDestinationId != R.id.main -> {
                            // 没有上一页且不在首页，尝试正常返回，如果失败则跳转到首页
                            if (BuildConfig.DEBUG) {
                                Timber.tag("FragmentNavigatorPlus").d("MainActivity handleOnBackPressed: preDestinationId为null且不在首页，尝试返回")
                            }
                            if (!it.popBackStack()) {
                                if (BuildConfig.DEBUG) {
                                    Timber.tag("FragmentNavigatorPlus").d("MainActivity handleOnBackPressed: 返回失败，跳转到首页")
                                }
                                MetaRouter.Main.tabHome(this@MainActivity)
                            }
                        }

                        curDestinationId == R.id.splash -> {
                            // 闪屏页，不处理
                            if (BuildConfig.DEBUG) {
                                Timber.tag("FragmentNavigatorPlus").d("MainActivity handleOnBackPressed: 在闪屏页，移除回调")
                            }
                            remove()
                        }

                        else -> {
                            if (BuildConfig.DEBUG) {
                                Timber.tag("FragmentNavigatorPlus").d("MainActivity handleOnBackPressed: 执行正常返回操作")
                            }
                            it.popBackStack()
                        }
                    }
                }
            }
            onBackPressedDispatcher.addCallback(this, callback)
            // 看起来没有什么实际作用（root定义的dialog展示时会不拦截，但是因为dialog用的key拦截，所以本身也没拦截），先去掉试试
//            val dialogNavigatorName =
//                it.navigatorProvider.getNavigator(DialogFragmentNavigator::class.java)
//                    .createDestination().navigatorName
//            it.addOnDestinationChangedListener { _, destination, _ ->
//                callback.isEnabled = destination.navigatorName != dialogNavigatorName
//            }
            it.addOnDestinationChangedListener { _, destination, _ ->
                callback.isEnabled = destination.id !in backPressIdList
            }
        }
        handleIntent(intent, metaKV.time.dayOnce(DAY_OPEN_APP))

        HostIntentStarter.init(application, this)
        registerMgsInviteCommand()
        registerScanResultListener()

        var roleLoad = true
        mainViewModel.mainItems.observe(this) {
            if (it.contains(MainBottomNavigationItem.EDITOR_HOME)) {
                if (roleLoad) {
                    roleLoad = false
                    if (MWBiz.isAvailable()) {
                        editorGameLoadInteractor.load(CategoryId.ROLE_GAME_PRE_LOAD, true)
                    } else {
                        MWLifeCallback.available.observe(this, true) { data ->
                            editorGameLoadInteractor.load(CategoryId.ROLE_GAME_PRE_LOAD, true)
                        }
                    }
                }
            } else {
                //没开底栏，也加载接口，获取角色游戏GameId
                editorGameLoadInteractor.preLoadGameConfig()
            }
        }
        EditorGameInteractHelper.registerMainActivity(this, this)
        gameSuggestionViewModel.init()
    }

    private fun initEpoxyExceptionHandler() {
        EpoxyController.setGlobalExceptionHandler { controller, exception ->
            // 记录详细的异常信息
            Timber.e(exception, "列表报错-Epoxy异常 - Controller: ${controller.javaClass.simpleName}")

            // 可以添加额外的处理逻辑
            // 比如上报到崩溃统计平台
            // 或者显示用户友好的错误提示
//            Analytics.track(EventConstants.EVENT_EPOXY_ERROR, "error" to exception.message ?: "")
        }
    }

    private fun setTextViewFactory2() {
        if (layoutInflater.factory == null && layoutInflater.factory2 == null) {
            LayoutInflaterCompat.setFactory2(layoutInflater, object : LayoutInflater.Factory2 {
                override fun onCreateView(parent: View?, name: String, context: Context, attrs: AttributeSet): View? {
                    val mName = if (name == "TextView" || name == "androidx.appcompat.widget.AppCompatTextView") {
                        MetaTextView::class.java.name
                    } else {
                        name
                    }
                    return PageMonitorLayoutInflaterFactory2.onCreateView(name, context, attrs) ?: delegate.createView(parent, mName, context, attrs)
                }

                override fun onCreateView(name: String, context: Context, attrs: AttributeSet): View? {
                    return null
                }
            })
        }
    }

    private fun registerScanResultListener() {
        supportFragmentManager.setFragmentResultListener(
            QRCodeScanFragment.KEY_REQUEST_KEY_GAME_TO_QR_CODE, this
        ) { _, bundle ->
            val request = ScanRequestData.from(bundle)
            val response = ScanResultData.from(bundle)

            if (response != null && request != null) {
                lifecycleScope.launch {
                    findNavHostFragment()?.let {
                        GlobalContext.get().get<QrCodeInteractor>().dispatchQRCodeScanResult(it, request, response)
                    }
                }
            }
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        handleIntent(intent)
    }

    private fun handleIntent(intent: Intent?, isDayFirst: Boolean = false) {
        if (intent == null) return
        val commonBack = intent.getBooleanExtra(KEY_COMMON_BACK, false)
        if (commonBack && navController().currentDestination != null && navController().currentDestination?.id != R.id.splash) {
            return
        }
        if (handlePush(intent, isDayFirst)) return

        val command = intent.getStringExtra(KEY_COMMAND)
        val source = intent.getStringExtra(PARAM_SOURCE_FROM) ?: LinkData.SOURCE_UNKNOWN
        if (MetaDeepLink.ACTION_LOGIN == command && source == LoginSource.ThirdAppAuthorize.source) {
            findNavHostFragment()?.let { MetaRouter.Login.login(it, loginSource = LoginSource.ThirdAppAuthorize) }
            return
        }
        // 开始dynamicLink解析
        val rawLink = intent.data
        DeeplinkAnalysisUtil.start(rawLink.toString())
        DynamicLinksWrapper.parseDynamicLinks(intent, this, succeed = { link ->
            Timber.d("checkcheck ${rawLink}, $link")
            val realLink = link ?: rawLink
            DeeplinkAnalysisUtil.parseSuccess(realLink.toString())
            realLink?.let {
                handleLink(it, false, source)
            } ?: run {
                DeeplinkAnalysisUtil.failed(rawLink.toString(), "analyze failed: success callback but link empty")
                getShareIdFromClipBoard()
            }
        }, failed = {
            Timber.w(it, "DynamicLink parse failed")
            // 华为就算用gpark://xxx的scheme，没有gms的话也会失败，所以做个兜底
            rawLink?.let {
                handleLink(it, true, source)
            } ?: run {
                DeeplinkAnalysisUtil.failed(rawLink.toString(), "analyze failed: failed callback and link empty-$it")
                getShareIdFromClipBoard()
            }
        })
    }

    /**
     * @param [link] scheme
     * @param [backup] 是否为兜底操作
     * @param [source] 来源 [com.socialplay.gpark.function.deeplink.LinkData]
     */
    private fun handleLink(link: Uri, backup: Boolean, source: String) {
        val result = findNavHostFragment()?.let {
            MetaDeepLink.handle(
                this, it, mainViewModel, link, source
            )
        }
        if (result?.succeeded == true) {
            // 处理了
            DeeplinkAnalysisUtil.distributeEnd(link.toString())
        } else {
            DeeplinkAnalysisUtil.failed(link.toString(), "during handle:${if (backup) "failed backup" else ""}_${result?.failedReason}")
        }
        if (ShareActiveHelper.jumpMain) {
            ShareActiveHelper.jumpMain = false
        } else {
            ShareActiveHelper.skipCheck()
        }
    }

    private fun handlePush(intent: Intent, isDayFirst: Boolean): Boolean {
        val link = intent.getStringExtra(MetaDeepLink.KEY_PUSH_LINK_VALUE)
        Timber.i("main_intent: extras: ${intent.extras} data: ${intent.data} link: $link intent_all: $intent")
        if (!link.isNullOrEmpty()) {
            val uri = Uri.parse(link)
            val jo = runCatching {
                JSONObject(
                    intent.getStringExtra(MetaDeepLink.KEY_PUSH_MSG_EXTRA) ?: ""
                ).toMap().toMutableMap()
            }.getOrElse {
                it.printStackTrace()
                mutableMapOf()
            }
            val gid = uri.getQueryParameter(MetaDeepLink.PARAM_GAME_ID)

            Analytics.track(EventConstants.FCM_PUSH_CLICK, jo.apply {
                put("action", uri.getQueryParameter(MetaDeepLink.KEY_ACTION).toString())
                put("start", if (isDayFirst) 0 else 1)
                if (!gid.isNullOrEmpty()) {
                    put("gameid", gid)
                }
            })
            findNavHostFragment()?.let {
                MetaDeepLink.handle(
                    this, it, mainViewModel, uri, LinkData.SOURCE_PUSH
                )
            }
            ShareActiveHelper.skipCheck()
            return true
        }
        return false
    }

    fun getShareIdFromClipBoard() {
        this.window.decorView.post {
            lifecycleScope.launch {
                val result = DispatchMgsShare.dispatchMgsShare(this@MainActivity, findNavHostFragment())
                if (result) {
                    ShareActiveHelper.skipCheck()
                }
            }
        }
    }


    fun findNavHostFragment(): NavHostFragment? {
        return supportFragmentManager.fragments.findTyped<NavHostFragment>()
    }

    private fun findCurrentFragment(): Fragment? {
        val navHostFragment = supportFragmentManager.fragments.findTyped<NavHostFragment>() ?: return null
        navHostFragment.childFragmentManager.fragments.forEach {
            if (it.isVisible) {
                if (it is MainFragment) {
                    it.childFragmentManager.fragments.forEach {
                        if (it.isVisible) {
                            return it
                        }
                    }
                }
                return it
            }
        }
        return null
    }


    override fun navigateUpTo(upIntent: Intent?): Boolean {
        return navController().navigateUp()
    }

    private fun navController() = findNavController(R.id.nav_host_fragment)

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onTokenInvalid(event: TokenInvalidEvent) {
        lifecycleScope.launch {
            logoutWhenTokenInvalid(event.accessTokenIsNullOrEmpty)
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSwitchProcess(event: ProcessSwitchEvent) {
        if (PandoraToggle.isOpenAdFake) {
            Timber.e("onSwitchProcess return isOpenAdFake! event: $event")
            return
        }
        Timber.d("onSwitchProcess: $event")
        val adSdkBridge: IAdSdkBridge = GlobalContext.get().get()
        adSdkBridge.addInitCallback {
            adSdkBridge.preLoadAds(event.gameId, event.gamePkg, this)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onBackToGame(event: BackToGameEvent) {
        Timber.d("onBackToGame taskId: ${event.taskId}")
        if (event.taskId > 0) {
            val am = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            am.moveTaskToFront(event.taskId, ActivityManager.MOVE_TASK_WITH_HOME)
        }
    }

    private suspend fun logoutWhenTokenInvalid(accessTokenIsNullOrEmpty: Boolean) {
        if (accessTokenIsNullOrEmpty) {
            Timber.tag(CheckTokenInterceptor.TAG).d("logoutWhenTokenInvalid accessTokenIsNullOrEmpty")
            return
        }
        logout2GuideLogin(LoginSource.TokenInvalid)
    }

    private suspend fun logout2GuideLogin(source: LoginSource) {
        if (loggingOut.getAndSet(true)) {
            Timber.tag(CheckTokenInterceptor.TAG).d("check_report logoutWhenTokenInvalid isDoingLogout")
            return
        }
        val accountInteractor = GlobalContext.get().get<AccountInteractor>()
        // token过期时不需要通知后端用户登出
        accountInteractor.logout(source !is LoginSource.TokenInvalid).collect {
            Timber.tag(CheckTokenInterceptor.TAG).d("check_report tokenInterceptor logout success source:${source}")
            if (it.succeeded) {
                showNoticeTokenInvalidDialog(source)
            }
            loggingOut.set(false)
        }
    }

    private fun showNoticeTokenInvalidDialog(source: LoginSource) {
        if (source is LoginSource.TokenInvalid) {
            ToastUtil.showLong(this, getString(R.string.token_invalid))
        }
        if (navController().currentDestination?.id != R.id.guideLogin) {
            findNavHostFragment()?.let { MetaRouter.Startup.guideLogin(it, LoginSource.TokenInvalid) }
        }
    }

    override fun onResume() {
        super.onResume()
        isFront.set(true)
        if (!isPad) {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }
        if (mainViewModel.accountInteractor.moduleGuideStatus == BaseAccountInteractor.MODULE_GUIDE_STATUS_GAME) {
            val navController = navController()
            mainViewModel.accountInteractor.moduleGuideStatus =
                BaseAccountInteractor.MODULE_GUIDE_STATUS_NEWBIE
            MetaRouter.UgcDesign.rookie(navController)
        }
    }

    override fun onPause() {
        super.onPause()
        isFront.set(false)
        AppLaunchAnalytics.hasPaused = true
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        unregisterMgsInvitationCommandListener()
        super.onDestroy()
    }

    private fun registerMgsInviteCommand() {
        CommandMessageRegistry.addMessageListener(violateWarnCmdListener)
        CommandMessageRegistry.addMessageListener(violateBanCmdListener)
        CommandMessageRegistry.addJSONAnalyzer<CmdViolateBanMessage>(FloatNoticeInteractor.TYPE_USER_LOGIN_LIMIT) //封禁
        CommandMessageRegistry.addJSONAnalyzer<CmdViolateWarnMessage>(FloatNoticeInteractor.TYPE_USER_REPORT_WARN) //警告
    }

    private val violateWarnCmdListener: suspend (CmdViolateWarnMessage) -> Unit = {
        Timber.d("check_report violateWarnCmdListener curUuid:${GlobalContext.get().get<AccountInteractor>().curUuid}, content:${it} ")
        lifecycleScope.launch {
            logout2GuideLogin(LoginSource.ViolateWarn)
        }
    }

    private val violateBanCmdListener: suspend (CmdViolateBanMessage) -> Unit = {
        Timber.d("check_report violateBanCmdListener curUuid:${GlobalContext.get().get<AccountInteractor>().curUuid}, content:${it} ")
        lifecycleScope.launch {
            logout2GuideLogin(LoginSource.ViolateBan)
        }
    }

    override fun onAppLocaleChange(oldLocale: Locale?, newLocale: Locale?) {

    }

    override fun onSystemLocaleChange(oldLocale: Locale?, newLocale: Locale?) {
        // todo 看看最后要哪种
//        MetaRouter.Settings.changeLanguage(this)
        MetaRouter.Main.restartProcess(this)
    }

    /**
     * 解除注册的MGS邀请透传消息监听器
     */
    private fun unregisterMgsInvitationCommandListener() {
        ImNotificationHandler.unRegisterCommand()
    }
}