package com.socialplay.gpark.app.initialize

import android.content.Context
import com.bumptech.glide.Glide
import com.bumptech.glide.GlideBuilder
import com.bumptech.glide.Registry
import com.bumptech.glide.annotation.GlideModule
import com.bumptech.glide.integration.okhttp3.OkHttpUrlLoader
import com.bumptech.glide.load.engine.bitmap_recycle.LruBitmapPool
import com.bumptech.glide.load.engine.executor.GlideExecutor
import com.bumptech.glide.load.model.GlideUrl
import com.bumptech.glide.module.AppGlideModule
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.di.provideGlideOkHttpClient
import timber.log.Timber
import java.io.InputStream

@GlideModule
class MetaAppGlideModule : AppGlideModule() {
    override fun applyOptions(context: Context, builder: GlideBuilder) {
        super.applyOptions(context, builder)
        if (BuildConfig.LOG_DEBUG) {
            builder.setLogLevel(android.util.Log.VERBOSE)
        }
        val bestThreadCount = GlideExecutor.calculateBestThreadCount()
        Timber.d("Glide best thread count: $bestThreadCount")
        builder.setSourceExecutor(
            GlideExecutor.newSourceBuilder()
                .setThreadCount(bestThreadCount * 2).build()
        )

        // 配置更安全的BitmapPool
        val maxMemory = Runtime.getRuntime().maxMemory()
        val bitmapPoolSize = (maxMemory / 8).toInt() // 使用1/8的内存作为BitmapPool大小

        // 使用默认的LruBitmapPool，但配置合适的大小
        // 我们通过其他方式来增强安全性，而不是继承LruBitmapPool
        builder.setBitmapPool(LruBitmapPool(bitmapPoolSize.toLong()))

        Timber.d("Glide BitmapPool size: ${bitmapPoolSize / 1024 / 1024}MB")
    }

    override fun registerComponents(context: Context, glide: Glide, registry: Registry) {
        super.registerComponents(context, glide, registry)
        registry.replace(
            GlideUrl::class.java, InputStream::class.java, OkHttpUrlLoader.Factory(
                provideGlideOkHttpClient().apply {
                    dispatcher.maxRequestsPerHost = 20
                    interceptors
                }
            ))
    }

    override fun isManifestParsingEnabled(): Boolean {
        return false
    }
}

