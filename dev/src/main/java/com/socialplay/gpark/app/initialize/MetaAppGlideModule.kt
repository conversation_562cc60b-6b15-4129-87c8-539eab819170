package com.socialplay.gpark.app.initialize

import android.content.Context
import android.graphics.Bitmap
import com.bumptech.glide.Glide
import com.bumptech.glide.GlideBuilder
import com.bumptech.glide.Registry
import com.bumptech.glide.annotation.GlideModule
import com.bumptech.glide.integration.okhttp3.OkHttpUrlLoader
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool
import com.bumptech.glide.load.engine.bitmap_recycle.LruBitmapPool
import com.bumptech.glide.load.engine.executor.GlideExecutor
import com.bumptech.glide.load.model.GlideUrl
import com.bumptech.glide.module.AppGlideModule
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.di.provideGlideOkHttpClient
import timber.log.Timber
import java.io.InputStream

@GlideModule
class MetaAppGlideModule : AppGlideModule() {
    override fun applyOptions(context: Context, builder: GlideBuilder) {
        super.applyOptions(context, builder)
        if (BuildConfig.LOG_DEBUG) {
            builder.setLogLevel(android.util.Log.VERBOSE)
        }
        val bestThreadCount = GlideExecutor.calculateBestThreadCount()
        Timber.d("Glide best thread count: $bestThreadCount")
        builder.setSourceExecutor(
            GlideExecutor.newSourceBuilder()
                .setThreadCount(bestThreadCount * 2).build()
        )

        // 配置更安全的BitmapPool
        val maxMemory = Runtime.getRuntime().maxMemory()
        val bitmapPoolSize = (maxMemory / 8).toInt() // 使用1/8的内存作为BitmapPool大小

        // 使用自定义的安全BitmapPool包装器
        val originalPool = LruBitmapPool(bitmapPoolSize.toLong())
        builder.setBitmapPool(SafeBitmapPoolWrapper(originalPool))

        Timber.d("Glide BitmapPool size: ${bitmapPoolSize / 1024 / 1024}MB")
    }

    override fun registerComponents(context: Context, glide: Glide, registry: Registry) {
        super.registerComponents(context, glide, registry)
        registry.replace(
            GlideUrl::class.java, InputStream::class.java, OkHttpUrlLoader.Factory(
                provideGlideOkHttpClient().apply {
                    dispatcher.maxRequestsPerHost = 20
                    interceptors
                }
            ))
    }

    override fun isManifestParsingEnabled(): Boolean {
        return false
    }
}

/**
 * 安全的BitmapPool包装器，防止已回收的Bitmap导致崩溃
 */
private class SafeBitmapPoolWrapper(private val delegate: BitmapPool) : BitmapPool {

    override fun put(bitmap: Bitmap?) {
        if (bitmap == null) {
            return
        }

        // 关键：在put之前检查Bitmap是否已被回收
        if (bitmap.isRecycled) {
            Timber.d("SafeBitmapPool: Skip putting recycled bitmap")
            return
        }

        try {
            // 再次检查，因为在多线程环境中Bitmap状态可能会改变
            if (!bitmap.isRecycled) {
                delegate.put(bitmap)
            }
        } catch (e: IllegalStateException) {
            // 捕获"Cannot obtain size for recycled Bitmap"异常
            if (e.message?.contains("recycled Bitmap") == true) {
                Timber.w("SafeBitmapPool: Attempted to put recycled bitmap, ignoring")
            } else {
                Timber.w(e, "SafeBitmapPool: Failed to put bitmap")
            }
        } catch (e: Exception) {
            Timber.w(e, "SafeBitmapPool: Unexpected error putting bitmap")
        }
    }

    override fun get(width: Int, height: Int, config: Bitmap.Config): Bitmap {
        return try {
            delegate.get(width, height, config)
        } catch (e: Exception) {
            Timber.w(e, "SafeBitmapPool: Failed to get bitmap, creating new one")
            Bitmap.createBitmap(width, height, config)
        }
    }

    override fun getDirty(width: Int, height: Int, config: Bitmap.Config): Bitmap {
        return try {
            delegate.getDirty(width, height, config)
        } catch (e: Exception) {
            Timber.w(e, "SafeBitmapPool: Failed to get dirty bitmap, creating new one")
            Bitmap.createBitmap(width, height, config)
        }
    }

    override fun clearMemory() {
        try {
            delegate.clearMemory()
        } catch (e: Exception) {
            Timber.w(e, "SafeBitmapPool: Failed to clear memory")
        }
    }

    override fun trimMemory(level: Int) {
        try {
            delegate.trimMemory(level)
        } catch (e: IllegalStateException) {
            // 捕获trimMemory过程中的recycled bitmap异常
            if (e.message?.contains("recycled Bitmap") == true) {
                Timber.w("SafeBitmapPool: Recycled bitmap detected during trimMemory, clearing pool")
                try {
                    delegate.clearMemory()
                } catch (clearException: Exception) {
                    Timber.e(clearException, "SafeBitmapPool: Failed to clear memory after recycled bitmap error")
                }
            } else {
                Timber.w(e, "SafeBitmapPool: Failed to trim memory")
            }
        } catch (e: Exception) {
            Timber.w(e, "SafeBitmapPool: Unexpected error during trimMemory")
        }
    }

    override fun getMaxSize(): Long {
        return try {
            delegate.maxSize
        } catch (e: Exception) {
            Timber.w(e, "SafeBitmapPool: Failed to get max size")
            0L
        }
    }

    override fun setSizeMultiplier(sizeMultiplier: Float) {
        try {
            delegate.setSizeMultiplier(sizeMultiplier)
        } catch (e: Exception) {
            Timber.w(e, "SafeBitmapPool: Failed to set size multiplier")
        }
    }
}

