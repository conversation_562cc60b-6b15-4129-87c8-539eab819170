package com.socialplay.gpark.function.share.platform

import android.app.Activity
import android.content.Context
import android.graphics.Bitmap
import com.meta.biz.ugc.model.GameCommonFeature
import com.meta.lib.mwbiz.MWBizBridge
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.share.DirectShareData
import com.socialplay.gpark.data.model.share.ShareData
import com.socialplay.gpark.data.model.share.ShareResult
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.mw.GameCommonFeatureResolver
import com.socialplay.gpark.function.mw.MWFeatureSupport
import com.socialplay.gpark.function.share.MetaShare
import com.socialplay.gpark.function.share.ShareWrapper
import com.socialplay.gpark.util.BitmapRecycleUtil
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.extension.registerHermes
import com.socialplay.gpark.util.extension.unregisterHermes
import org.greenrobot.eventbus.Subscribe
import org.koin.core.context.GlobalContext
import java.io.File
import java.io.FileOutputStream

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/08/13
 *     desc   :
 * </pre>
 */
object ShareHelper {

    const val MEDIA_NONE = 0
    const val MEDIA_IMAGE = 1
    const val MEDIA_VIDEO = 2

    const val SCENE_UNKNOWN = "unknown"
    const val SCENE_PROFILE = "profile"
    const val SCENE_PGC_DETAIL = "pgcDetail"
    const val SCENE_UGC_DETAIL = "ugcDetail"
    const val SCENE_POST_DETAIL = "postDetail"
    const val SCENE_VIDEO_FEED = "videoFeed"
    const val SCENE_SCREENSHOT = "screenshot"
    const val SCENE_OC_MOMENT = "ocMoment"
    const val SCENE_UGC_DESIGN_DETAIL = "ugcDesignDetail"
    const val SCENE_AVATAR_SCREENSHOT = "avatarScreenshot"

    const val MODE_SINGLE_IMAGE = "singleImage"
    const val MODE_MULTI_IMAGES = "multiImages"
    const val MODE_SINGLE_VIDEO = "singleVideo"
    const val MODE_SINGLE_WEB_CARD = "singleWebCard"

    const val CODE_OK = 200
    const val CODE_UNSUPPORTED_PLATFORM = 1000
    const val CODE_UNSUPPORTED_MODE = 1001
    const val CODE_INVALID_PARAMS = 1002
    const val CODE_NOT_INSTALLED = 1003
    const val CODE_CANCELLED = 1004
    const val CODE_VERSION_OUTDATED = 1005
    const val CODE_SDK_ERROR = 1100

    private var deleteMark = false

    private var directShareData: DirectShareData? = null
    private var directGameId: String = ""
    private var directMessageId: Int = 0
    private var directCurrentGameId: String = ""

    val downloadPage
        get() = GlobalContext.get().get<H5PageConfigInteractor>()
            .getH5PageUrl(H5PageConfigInteractor.APP_DOWNLOAD)

    fun getShareRecordId4Pgc(gameId: String) = "${SCENE_PGC_DETAIL}_${gameId}"
    fun getShareRecordId4Ugc(gameId: String) = "${SCENE_UGC_DETAIL}_${gameId}"
    fun getShareRecordId4Post(postId: String) = "${SCENE_POST_DETAIL}_${postId}"

    fun getCacheDir(context: Context): File {
        return File(context.cacheDir, "global_share_cache").apply {
            mkdirs()
            if (!deleteMark) {
                runCatching {
                    deleteOnExit()
                    deleteMark = true
                }
            }
        }
    }

    fun cacheFile(context: Context, file: File, filename: String): File {
        val targetFile = File(getCacheDir(context), filename)
        return if (targetFile.exists()) {
            targetFile
        } else if (file.copyRecursively(
                targetFile,
                overwrite = true,
                onError = { _, _ -> OnErrorAction.SKIP })
        ) {
            targetFile
        } else {
            file
        }
    }

    fun getFile(context: Context, filename: String): Pair<Boolean, File> {
        val targetFile = File(getCacheDir(context), filename)
        return targetFile.exists() to targetFile
    }

    fun saveBitmap(
        context: Context,
        bitmap: Bitmap,
        filename: String,
        compressFormat: Bitmap.CompressFormat = Bitmap.CompressFormat.JPEG,
        quality: Int = 100
    ) = kotlin.runCatching {
        if (!bitmap.isRecycled) {
            val (exists, targetFile) = getFile(context, filename)
            if (exists) {
                targetFile.delete()
            }
            val out = FileOutputStream(targetFile)
            if (bitmap.compress(compressFormat, quality, out)) {
                out.flush()
                out.close()
            }
            BitmapRecycleUtil.safeRecycle(bitmap)
            targetFile
        } else {
            null
        }
    }.getOrNull()

    fun saveRequestId(shareData: ShareData) {
        GlobalContext.get().get<MetaKV>().appKV.shareRequestId = shareData.requestId

    }

    fun getRequestId(): String {
        return GlobalContext.get().get<MetaKV>().appKV.shareRequestId
    }

    suspend fun testDirectShare(
        context: Activity,
        directShareData: DirectShareData
    ) {
        directShare(
            context,
            GameCommonFeature().apply {
                gameId = MWBizBridge.currentGameId()
                params = mapOf("shareData" to GsonUtil.safeToJson(directShareData))
            },
            0,
            MWBizBridge.currentGameId()
        )
    }

    suspend fun directShare(
        context: Activity,
        msg: GameCommonFeature,
        messageId: Int,
        currentGameId: String
    ) {
        directGameId = msg.gameId
        directMessageId = messageId
        directCurrentGameId = currentGameId
        this.directShareData = null
        val directShareData = GsonUtil.gsonSafeParseCollection<DirectShareData>(
            msg.params?.get("shareData")?.toString()
        )
        if (directShareData == null) {
            notifyDirectShareResult(code = CODE_INVALID_PARAMS)
            return
        }
        <EMAIL> = directShareData

        val analyticsMap = mutableMapOf("gameid" to directCurrentGameId)
        if (directShareData.trackParams != null) {
            analyticsMap.putAll(directShareData.trackParams)
        }
        Analytics.track(
            EventConstants.EVENT_MOMENTS_SHARE_CLICK,
            analyticsMap
        )

        val code = directShareData.validate(context)
        if (code != CODE_OK) {
            notifyDirectShareResult(code = code)
            return
        }
        val shareData = directShareData.toShareDataWithConfig(context).copy(gameId = currentGameId)
        registerHermes()

        MetaShare.share(context, shareData)
    }

    private fun notifyDirectShareResult(
        code: Int,
        msg: String? = null,
        sdkCode1: Int? = null,
        sdkCode2: Int? = null
    ) {
        val params: HashMap<String, Any?> = hashMapOf(
            "code" to code,
            "errMsg" to msg.orEmpty()
        )
        directShareData?.let {
            val type = ShareWrapper.sharePlatformToTypeId(it.platform)
            if (type != null) {
                Analytics.track(
                    EventConstants.EVENT_MOMENTS_SHARE_RESULT,
                    "gameid" to directCurrentGameId,
                    "type" to type,
                    "result" to (if (code == 200) "1" else "0")
                )
            }
            params["requestId"] = it.requestId
            params["platform"] = it.platform
            params["mode"] = it.mode
            if (sdkCode1 != null) {
                params["sdkCode1"] = sdkCode1
            }
            if (sdkCode2 != null) {
                params["sdkCode2"] = sdkCode2
            }
        }
        GameCommonFeatureResolver.callUECommonResult(
            directGameId,
            directMessageId,
            MWFeatureSupport.COMMON_SHARE_DIRECT,
            params
        )

        directShareData = null
        directGameId = ""
        directMessageId = 0
        directCurrentGameId = ""
    }

    @Subscribe
    fun onEvent(event: ShareResult) {
        directShareData?.let {
            if (event.match(it)) {
                notifyDirectShareResult(
                    code = if (event.isSuccess) {
                        CODE_OK
                    } else if (event.isCancel) {
                        CODE_CANCELLED
                    } else {
                        CODE_SDK_ERROR
                    },
                    msg = if (event.isFail) event.msg.orEmpty() else "",
                    sdkCode1 = event.sdkCode1,
                    sdkCode2 = event.sdkCode2
                )
                unregisterHermes()
            }
        }
    }
}