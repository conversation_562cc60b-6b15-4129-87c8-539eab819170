package com.socialplay.gpark.function.navigation

import android.util.Log
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import com.socialplay.gpark.BuildConfig
import timber.log.Timber

/**
 * Navigation状态恢复工具
 * 
 * 用于处理Navigation组件与FragmentManager状态不一致的问题
 */
object NavigationStateRecovery {
    
    private const val TAG = "NavigationStateRecovery"
    
    /**
     * 检查并修复Navigation状态不一致
     * 
     * @param navController Navigation控制器
     * @param fragmentManager Fragment管理器
     * @return 是否检测到并尝试修复了状态不一致
     */
    fun checkAndFixStateInconsistency(
        navController: NavController,
        fragmentManager: FragmentManager
    ): Boolean {
        try {
            val inconsistency = detectStateInconsistency(navController, fragmentManager)
            
            if (inconsistency.hasInconsistency) {
                if (BuildConfig.DEBUG) {
                    Timber.tag(TAG).w("检测到Navigation状态不一致:")
                    Timber.tag(TAG).w("  Navigation当前: ${inconsistency.navCurrentDestination}")
                    Timber.tag(TAG).w("  Fragment当前: ${inconsistency.fragmentCurrentName}")
                    Timber.tag(TAG).w("  Navigation栈大小: ${inconsistency.navStackSize}")
                    Timber.tag(TAG).w("  Fragment栈大小: ${inconsistency.fragmentStackSize}")
                }
                
                // 尝试修复状态不一致
                return attemptStateRecovery(navController, fragmentManager, inconsistency)
            }
            
            return false
            
        } catch (e: Exception) {
            if (BuildConfig.DEBUG) {
                Timber.tag(TAG).e(e, "检查状态一致性时发生错误")
            }
            return false
        }
    }
    
    /**
     * 检测状态不一致
     */
    private fun detectStateInconsistency(
        navController: NavController,
        fragmentManager: FragmentManager
    ): StateInconsistency {
        // Navigation状态
        val navCurrentEntry = navController.currentBackStackEntry
        val navCurrentDestination = navCurrentEntry?.destination?.displayName ?: "null"
        val navStackSize = navController.backQueue.size
        
        // Fragment状态
        val primaryFragment = fragmentManager.primaryNavigationFragment
        val fragmentCurrentName = primaryFragment?.javaClass?.simpleName ?: "null"
        val fragmentStackSize = fragmentManager.backStackEntryCount
        val activeFragments = fragmentManager.fragments.filter { !it.isRemoving }
        
        // 检查是否存在不一致
        val hasInconsistency = when {
            // Navigation认为在MainFragment，但Fragment实际不是
            navCurrentDestination.contains("main", ignoreCase = true) && 
            !fragmentCurrentName.contains("MainFragment", ignoreCase = true) -> true
            
            // Navigation栈和Fragment栈大小差异过大
            kotlin.math.abs(navStackSize - fragmentStackSize) > 2 -> true
            
            // 存在孤儿Fragment
            activeFragments.any { fragment ->
                val tag = fragment.tag
                tag != null && !isFragmentInNavigationStack(tag, navController)
            } -> true
            
            else -> false
        }
        
        return StateInconsistency(
            hasInconsistency = hasInconsistency,
            navCurrentDestination = navCurrentDestination,
            fragmentCurrentName = fragmentCurrentName,
            navStackSize = navStackSize,
            fragmentStackSize = fragmentStackSize,
            activeFragments = activeFragments,
            orphanFragments = activeFragments.filter { fragment ->
                val tag = fragment.tag
                tag != null && !isFragmentInNavigationStack(tag, navController)
            }
        )
    }
    
    /**
     * 尝试恢复状态
     */
    private fun attemptStateRecovery(
        navController: NavController,
        fragmentManager: FragmentManager,
        inconsistency: StateInconsistency
    ): Boolean {
        try {
            if (BuildConfig.DEBUG) {
                Timber.tag(TAG).d("尝试恢复Navigation状态...")
            }
            
            // 策略1: 如果Fragment状态看起来更可靠，尝试同步Navigation状态
            val primaryFragment = fragmentManager.primaryNavigationFragment
            if (primaryFragment != null) {
                val fragmentName = primaryFragment.javaClass.simpleName
                
                when {
                    fragmentName.contains("MainFragment") -> {
                        // 如果当前Fragment是MainFragment，但Navigation不在main，尝试导航到main
                        val currentDestId = navController.currentDestination?.id
                        if (currentDestId != com.socialplay.gpark.R.id.main) {
                            if (BuildConfig.DEBUG) {
                                Timber.tag(TAG).d("尝试同步Navigation到MainFragment")
                            }
                            // 这里可以尝试导航到main，但要小心避免循环
                            return true
                        }
                    }
                    
                    fragmentName.contains("SplashFragment") -> {
                        // 如果当前Fragment是SplashFragment，检查是否应该在这里
                        if (BuildConfig.DEBUG) {
                            Timber.tag(TAG).d("检测到SplashFragment，检查是否合理")
                        }
                        // 通常SplashFragment应该只在应用启动时出现
                        return false // 暂时不处理
                    }
                }
            }
            
            // 策略2: 清理孤儿Fragment（非常保守）
            if (inconsistency.orphanFragments.isNotEmpty()) {
                if (BuildConfig.DEBUG) {
                    Timber.tag(TAG).d("发现${inconsistency.orphanFragments.size}个孤儿Fragment，但暂时不自动清理")
                }
                // 暂时不自动清理，只记录
            }
            
            return false
            
        } catch (e: Exception) {
            if (BuildConfig.DEBUG) {
                Timber.tag(TAG).e(e, "状态恢复失败")
            }
            return false
        }
    }
    
    /**
     * 检查Fragment是否在Navigation栈中
     */
    private fun isFragmentInNavigationStack(fragmentTag: String, navController: NavController): Boolean {
        return try {
            navController.backQueue.any { entry ->
                entry.id == fragmentTag
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取Navigation状态摘要
     */
    fun getNavigationStateSummary(
        navController: NavController,
        fragmentManager: FragmentManager
    ): String {
        return try {
            val navCurrent = navController.currentBackStackEntry?.destination?.displayName ?: "null"
            val navStackSize = navController.backQueue.size
            val fragmentCurrent = fragmentManager.primaryNavigationFragment?.javaClass?.simpleName ?: "null"
            val fragmentStackSize = fragmentManager.backStackEntryCount
            val activeFragmentCount = fragmentManager.fragments.filter { !it.isRemoving }.size
            
            buildString {
                appendLine("=== Navigation状态摘要 ===")
                appendLine("Navigation当前: $navCurrent")
                appendLine("Navigation栈大小: $navStackSize")
                appendLine("Fragment当前: $fragmentCurrent")
                appendLine("Fragment栈大小: $fragmentStackSize")
                appendLine("活跃Fragment数量: $activeFragmentCount")
                appendLine("========================")
            }
        } catch (e: Exception) {
            "状态摘要获取失败: ${e.message}"
        }
    }
    
    /**
     * 状态不一致信息
     */
    private data class StateInconsistency(
        val hasInconsistency: Boolean,
        val navCurrentDestination: String,
        val fragmentCurrentName: String,
        val navStackSize: Int,
        val fragmentStackSize: Int,
        val activeFragments: List<Fragment>,
        val orphanFragments: List<Fragment>
    )
}
