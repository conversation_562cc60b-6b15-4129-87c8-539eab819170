package com.socialplay.gpark.debug

import android.os.Bundle
import android.widget.Button
import android.widget.ScrollView
import android.widget.TextView
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import com.socialplay.gpark.BuildConfig
import timber.log.Timber

/**
 * Navigation修复测试Activity
 * 
 * 仅在Debug版本中可用，用于测试和验证Navigation状态修复功能
 */
class NavigationFixTestActivity : AppCompatActivity() {
    
    private lateinit var statusTextView: TextView
    private lateinit var logTextView: TextView
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        if (!BuildConfig.DEBUG) {
            finish()
            return
        }
        
        setupUI()
        updateStatus()
    }
    
    private fun setupUI() {
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(32, 32, 32, 32)
        }
        
        // 标题
        val titleText = TextView(this).apply {
            text = "Navigation状态诊断工具"
            textSize = 20f
            setPadding(0, 0, 0, 32)
        }
        layout.addView(titleText)
        
        // 状态显示
        statusTextView = TextView(this).apply {
            text = "正在加载状态..."
            textSize = 14f
            setPadding(16, 16, 16, 16)
            setBackgroundColor(0xFFE0E0E0.toInt())
        }
        layout.addView(statusTextView)
        
        // 按钮区域
        val buttonLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(0, 32, 0, 32)
        }
        
        // 刷新状态按钮
        val refreshButton = Button(this).apply {
            text = "刷新状态"
            setOnClickListener { updateStatus() }
        }
        buttonLayout.addView(refreshButton)
        
        // 打印详细状态按钮
        val printDetailButton = Button(this).apply {
            text = "打印详细状态"
            setOnClickListener { printDetailedState() }
        }
        buttonLayout.addView(printDetailButton)
        
        // 诊断问题按钮
        val diagnoseButton = Button(this).apply {
            text = "诊断问题"
            setOnClickListener { diagnoseIssues() }
        }
        buttonLayout.addView(diagnoseButton)
        
        // 尝试自动修复按钮
        val autoFixButton = Button(this).apply {
            text = "尝试自动修复"
            setOnClickListener { attemptAutoFix() }
        }
        buttonLayout.addView(autoFixButton)
        
        // 生成Bug报告按钮
        val bugReportButton = Button(this).apply {
            text = "生成Bug报告"
            setOnClickListener { generateBugReport() }
        }
        buttonLayout.addView(bugReportButton)
        
        layout.addView(buttonLayout)
        
        // 日志显示区域
        val logTitle = TextView(this).apply {
            text = "操作日志:"
            textSize = 16f
            setPadding(0, 16, 0, 8)
        }
        layout.addView(logTitle)
        
        logTextView = TextView(this).apply {
            text = "等待操作...\n"
            textSize = 12f
            setPadding(16, 16, 16, 16)
            setBackgroundColor(0xFFF5F5F5.toInt())
            maxLines = 20
        }
        
        val scrollView = ScrollView(this).apply {
            addView(logTextView)
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                400
            )
        }
        layout.addView(scrollView)
        
        setContentView(layout)
    }
    
    private fun updateStatus() {
        try {
            val status = NavigationDebugHelper.quickStatusCheck(this)
            statusTextView.text = "当前状态: $status"
            addLog("状态已更新: $status")
        } catch (e: Exception) {
            statusTextView.text = "状态获取失败: ${e.message}"
            addLog("状态更新失败: ${e.message}")
        }
    }
    
    private fun printDetailedState() {
        try {
            NavigationDebugHelper.printFullNavigationState(this)
            addLog("详细状态已打印到日志")
        } catch (e: Exception) {
            addLog("打印详细状态失败: ${e.message}")
        }
    }
    
    private fun diagnoseIssues() {
        try {
            val issues = NavigationDebugHelper.diagnoseNavigationIssues(this)
            if (issues.isEmpty()) {
                addLog("诊断完成: 未发现问题")
            } else {
                addLog("诊断完成: 发现${issues.size}个问题")
                issues.forEachIndexed { index, issue ->
                    addLog("  ${index + 1}. $issue")
                }
            }
        } catch (e: Exception) {
            addLog("诊断失败: ${e.message}")
        }
    }
    
    private fun attemptAutoFix() {
        try {
            val fixed = NavigationDebugHelper.attemptAutoFix(this)
            if (fixed) {
                addLog("自动修复: 已尝试修复")
                updateStatus() // 更新状态显示
            } else {
                addLog("自动修复: 未检测到需要修复的问题")
            }
        } catch (e: Exception) {
            addLog("自动修复失败: ${e.message}")
        }
    }
    
    private fun generateBugReport() {
        try {
            val report = NavigationDebugHelper.generateBugReport(this)
            addLog("Bug报告已生成:")
            addLog(report)
            
            // 也打印到Timber日志
            Timber.tag("NavigationBugReport").d(report)
        } catch (e: Exception) {
            addLog("Bug报告生成失败: ${e.message}")
        }
    }
    
    private fun addLog(message: String) {
        val timestamp = System.currentTimeMillis() % 100000 // 简化的时间戳
        val logMessage = "[$timestamp] $message\n"
        
        runOnUiThread {
            val currentText = logTextView.text.toString()
            val lines = currentText.split('\n').toMutableList()
            
            // 保持最多20行日志
            if (lines.size > 20) {
                lines.removeAt(0)
            }
            
            lines.add(logMessage.trimEnd())
            logTextView.text = lines.joinToString("\n")
        }
    }
}
