package com.socialplay.gpark.debug

import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentActivity
import androidx.navigation.fragment.NavHostFragment
import com.socialplay.gpark.BuildConfig
import timber.log.Timber

/**
 * 简单的Navigation测试工具
 * 
 * 提供基本的Navigation状态检查功能，避免复杂的反射操作
 */
object SimpleNavigationTest {
    
    private const val TAG = "SimpleNavigationTest"
    
    /**
     * 简单的状态检查
     */
    fun simpleStatusCheck(activity: AppCompatActivity): String {
        return try {
            val navHostFragment = activity.supportFragmentManager
                .findFragmentById(com.socialplay.gpark.R.id.nav_host_fragment) as? NavHostFragment
                ?: return "NavHostFragment未找到"
            
            val navController = navHostFragment.navController
            val fragmentManager = navHostFragment.childFragmentManager
            
            val navCurrent = navController.currentDestination?.displayName?.substringAfterLast('.') ?: "null"
            val fragmentCurrent = fragmentManager.primaryNavigationFragment?.javaClass?.simpleName ?: "null"
            val fragmentStackSize = fragmentManager.backStackEntryCount
            val activeFragmentCount = fragmentManager.fragments.filter { !it.isRemoving }.size
            
            "Nav:$navCurrent Frag:$fragmentCurrent Stack:$fragmentStackSize Active:$activeFragmentCount"
            
        } catch (e: Exception) {
            "检查失败: ${e.message}"
        }
    }
    
    /**
     * 检查是否存在状态不一致
     */
    fun checkInconsistency(activity: AppCompatActivity): Boolean {
        return try {
            val navHostFragment = activity.supportFragmentManager
                .findFragmentById(com.socialplay.gpark.R.id.nav_host_fragment) as? NavHostFragment
                ?: return false
            
            val navController = navHostFragment.navController
            val fragmentManager = navHostFragment.childFragmentManager
            
            val navCurrent = navController.currentDestination?.displayName ?: ""
            val fragmentCurrent = fragmentManager.primaryNavigationFragment?.javaClass?.simpleName ?: ""
            val fragmentStackSize = fragmentManager.backStackEntryCount
            
            // 简单的不一致检查
            val navSaysMain = navCurrent.contains("main", ignoreCase = true)
            val fragmentIsMain = fragmentCurrent.contains("MainFragment", ignoreCase = true)
            
            // 如果Navigation认为在main但Fragment不是MainFragment，且有回退栈，可能存在不一致
            navSaysMain && !fragmentIsMain && fragmentStackSize > 0
            
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 打印详细状态到日志
     */
    fun printDetailedStatus(activity: AppCompatActivity) {
        if (!BuildConfig.DEBUG) return
        
        try {
            val navHostFragment = activity.supportFragmentManager
                .findFragmentById(com.socialplay.gpark.R.id.nav_host_fragment) as? NavHostFragment
            
            if (navHostFragment == null) {
                Timber.tag(TAG).w("NavHostFragment未找到")
                return
            }
            
            val navController = navHostFragment.navController
            val fragmentManager = navHostFragment.childFragmentManager
            
            val report = buildString {
                appendLine("=== Navigation状态报告 ===")
                appendLine("Navigation当前: ${navController.currentDestination?.displayName}")
                appendLine("Navigation当前ID: ${navController.currentDestination?.id}")
                appendLine("Fragment主导航: ${fragmentManager.primaryNavigationFragment?.javaClass?.simpleName}")
                appendLine("Fragment回退栈大小: ${fragmentManager.backStackEntryCount}")
                appendLine("活跃Fragment数量: ${fragmentManager.fragments.filter { !it.isRemoving }.size}")
                
                appendLine("Fragment回退栈:")
                for (i in 0 until fragmentManager.backStackEntryCount) {
                    val entry = fragmentManager.getBackStackEntryAt(i)
                    appendLine("  [$i] ${entry.name}")
                }
                
                appendLine("活跃Fragment:")
                fragmentManager.fragments.filter { !it.isRemoving }.forEachIndexed { index, fragment ->
                    appendLine("  [$index] ${fragment.javaClass.simpleName} (${fragment.tag}) - ${fragment.lifecycle.currentState}")
                }
                
                val hasInconsistency = checkInconsistency(activity)
                appendLine("状态不一致: $hasInconsistency")
                
                appendLine("=== 报告结束 ===")
            }
            
            Timber.tag(TAG).d(report)
            
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "打印状态失败")
        }
    }
    
    /**
     * 生成简单的Bug报告
     */
    fun generateSimpleBugReport(activity: AppCompatActivity): String {
        return try {
            val status = simpleStatusCheck(activity)
            val hasInconsistency = checkInconsistency(activity)
            val timestamp = System.currentTimeMillis()
            
            buildString {
                appendLine("=== Navigation Bug Report ===")
                appendLine("时间: $timestamp")
                appendLine("状态: $status")
                appendLine("存在不一致: $hasInconsistency")
                
                if (hasInconsistency) {
                    appendLine("问题: Navigation与Fragment状态不一致")
                    appendLine("建议: 检查返回键处理逻辑")
                }
                
                appendLine("=== 报告结束 ===")
            }
        } catch (e: Exception) {
            "Bug报告生成失败: ${e.message}"
        }
    }
    
    /**
     * 测试修复是否有效
     */
    fun testFixEffectiveness(activity: AppCompatActivity): String {
        return try {
            val beforeStatus = simpleStatusCheck(activity)
            val beforeInconsistency = checkInconsistency(activity)
            
            if (BuildConfig.DEBUG) {
                Timber.tag(TAG).d("修复前状态: $beforeStatus")
                Timber.tag(TAG).d("修复前不一致: $beforeInconsistency")
            }
            
            // 这里可以触发一些操作来测试修复效果
            // 例如模拟返回键操作等
            
            val afterStatus = simpleStatusCheck(activity)
            val afterInconsistency = checkInconsistency(activity)
            
            if (BuildConfig.DEBUG) {
                Timber.tag(TAG).d("修复后状态: $afterStatus")
                Timber.tag(TAG).d("修复后不一致: $afterInconsistency")
            }
            
            buildString {
                appendLine("修复效果测试:")
                appendLine("修复前: $beforeStatus")
                appendLine("修复后: $afterStatus")
                appendLine("不一致改善: ${beforeInconsistency} -> ${afterInconsistency}")
            }
            
        } catch (e: Exception) {
            "测试失败: ${e.message}"
        }
    }
}
