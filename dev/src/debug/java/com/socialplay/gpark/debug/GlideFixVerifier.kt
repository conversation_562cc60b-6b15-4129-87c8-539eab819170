package com.socialplay.gpark.debug

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import com.bumptech.glide.Glide
import com.socialplay.gpark.util.GlideCrashFix
import com.socialplay.gpark.util.GlideRecycledBitmapFix
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * Glide修复验证器
 * 
 * 用于验证Glide崩溃修复是否正确安装和工作
 */
object GlideFixVerifier {
    
    private const val TAG = "GlideFixVerifier"
    
    /**
     * 验证所有修复是否正确安装
     */
    fun verifyAllFixes(context: Context) {
        Timber.tag(TAG).d("Starting Glide fix verification...")
        
        GlobalScope.launch(Dispatchers.Main) {
            try {
                // 1. 验证异常处理器
                verifyExceptionHandler()
                
                // 2. 验证BitmapPool修复
                verifyBitmapPoolFix()
                
                // 3. 验证反射修复
                verifyReflectionFix()
                
                // 4. 验证Bitmap回收安全性
                verifyBitmapRecycleSafety()
                
                // 5. 模拟内存压力测试
                simulateMemoryPressure(context)
                
                Timber.tag(TAG).d("All Glide fix verifications completed")
                
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "Error during Glide fix verification")
            }
        }
    }
    
    /**
     * 验证异常处理器是否安装
     */
    private fun verifyExceptionHandler() {
        val currentHandler = Thread.getDefaultUncaughtExceptionHandler()
        val isInstalled = currentHandler?.javaClass?.simpleName?.contains("GlideExceptionHandler") == true
        
        Timber.tag(TAG).d("Exception handler verification:")
        Timber.tag(TAG).d("  Current handler: ${currentHandler?.javaClass?.simpleName}")
        Timber.tag(TAG).d("  Glide handler installed: $isInstalled")
        
        if (!isInstalled) {
            Timber.tag(TAG).w("Glide exception handler not detected!")
        }
    }
    
    /**
     * 验证BitmapPool修复
     */
    private fun verifyBitmapPoolFix() {
        try {
            val poolStatus = GlideCrashFix.getBitmapPoolStatus()
            Timber.tag(TAG).d("BitmapPool fix verification:")
            Timber.tag(TAG).d("  Pool status: $poolStatus")
            
            // 尝试创建和回收一些Bitmap
            val testBitmap = Bitmap.createBitmap(100, 100, Bitmap.Config.ARGB_8888)
            testBitmap.eraseColor(Color.RED)
            
            val canPut = GlideCrashFix.canSafelyPutToBitmapPool(testBitmap)
            Timber.tag(TAG).d("  Can safely put test bitmap: $canPut")
            
            if (canPut) {
                val success = GlideCrashFix.safePutToBitmapPool(testBitmap)
                Timber.tag(TAG).d("  Successfully put test bitmap: $success")
            }
            
        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "BitmapPool fix verification failed")
        }
    }
    
    /**
     * 验证反射修复
     */
    private fun verifyReflectionFix() {
        try {
            val isApplied = GlideRecycledBitmapFix.isFixApplied()
            val poolInfo = GlideRecycledBitmapFix.getBitmapPoolInfo()
            
            Timber.tag(TAG).d("Reflection fix verification:")
            Timber.tag(TAG).d("  Fix applied: $isApplied")
            Timber.tag(TAG).d("  Pool info: $poolInfo")
            
            if (!isApplied) {
                Timber.tag(TAG).d("  Attempting to apply reflection fix...")
                GlideRecycledBitmapFix.applyFix()
                
                val newStatus = GlideRecycledBitmapFix.isFixApplied()
                Timber.tag(TAG).d("  Fix applied after attempt: $newStatus")
            }
            
        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "Reflection fix verification failed")
        }
    }
    
    /**
     * 验证Bitmap回收安全性
     */
    private fun verifyBitmapRecycleSafety() {
        try {
            Timber.tag(TAG).d("Bitmap recycle safety verification:")
            
            // 创建一些测试Bitmap
            val bitmaps = mutableListOf<Bitmap>()
            repeat(5) { i ->
                val bitmap = Bitmap.createBitmap(50, 50, Bitmap.Config.ARGB_8888)
                bitmap.eraseColor(Color.BLUE)
                bitmaps.add(bitmap)
                Timber.tag(TAG).d("  Created test bitmap $i")
            }
            
            // 手动回收一些Bitmap
            bitmaps.take(2).forEach { bitmap ->
                bitmap.recycle()
                Timber.tag(TAG).d("  Manually recycled bitmap")
            }
            
            // 尝试通过安全回收工具回收所有Bitmap（包括已回收的）
            bitmaps.forEach { bitmap ->
                try {
                    com.socialplay.gpark.util.BitmapRecycleUtil.safeRecycle(bitmap)
                    Timber.tag(TAG).d("  Safe recycle completed for bitmap")
                } catch (e: Exception) {
                    Timber.tag(TAG).w(e, "  Safe recycle failed for bitmap")
                }
            }
            
            Timber.tag(TAG).d("Bitmap recycle safety verification completed")
            
        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "Bitmap recycle safety verification failed")
        }
    }
    
    /**
     * 模拟内存压力测试
     */
    private fun simulateMemoryPressure(context: Context) {
        try {
            Timber.tag(TAG).d("Memory pressure simulation:")
            
            // 获取当前内存状态
            val runtime = Runtime.getRuntime()
            val maxMemory = runtime.maxMemory()
            val totalMemory = runtime.totalMemory()
            val freeMemory = runtime.freeMemory()
            val usedMemory = totalMemory - freeMemory
            
            Timber.tag(TAG).d("  Max memory: ${maxMemory / 1024 / 1024}MB")
            Timber.tag(TAG).d("  Used memory: ${usedMemory / 1024 / 1024}MB")
            Timber.tag(TAG).d("  Free memory: ${freeMemory / 1024 / 1024}MB")
            
            // 模拟内存清理
            val memoryLevels = listOf(
                android.content.ComponentCallbacks2.TRIM_MEMORY_UI_HIDDEN,
                android.content.ComponentCallbacks2.TRIM_MEMORY_BACKGROUND,
                android.content.ComponentCallbacks2.TRIM_MEMORY_MODERATE
            )
            
            memoryLevels.forEach { level ->
                Timber.tag(TAG).d("  Simulating memory trim level: $level")
                try {
                    GlideCrashFix.safeTrimGlideMemory(level)
                    Timber.tag(TAG).d("  Memory trim completed successfully")
                } catch (e: Exception) {
                    Timber.tag(TAG).w(e, "  Memory trim failed")
                }
            }
            
            // 强制垃圾回收
            System.gc()
            Timber.tag(TAG).d("  Forced garbage collection")
            
            // 检查内存状态
            val newFreeMemory = Runtime.getRuntime().freeMemory()
            Timber.tag(TAG).d("  Free memory after cleanup: ${newFreeMemory / 1024 / 1024}MB")
            
        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "Memory pressure simulation failed")
        }
    }
    
    /**
     * 获取修复状态摘要
     */
    fun getFixStatusSummary(): String {
        return try {
            val handler = Thread.getDefaultUncaughtExceptionHandler()
            val handlerInstalled = handler?.javaClass?.simpleName?.contains("GlideExceptionHandler") == true
            val reflectionApplied = GlideRecycledBitmapFix.isFixApplied()
            val poolStatus = GlideCrashFix.getBitmapPoolStatus()
            
            buildString {
                appendLine("=== Glide Fix Status Summary ===")
                appendLine("Exception Handler: ${if (handlerInstalled) "✓ Installed" else "✗ Not Installed"}")
                appendLine("Reflection Fix: ${if (reflectionApplied) "✓ Applied" else "✗ Not Applied"}")
                appendLine("BitmapPool: $poolStatus")
                appendLine("================================")
            }
        } catch (e: Exception) {
            "Fix status unavailable: ${e.message}"
        }
    }
}
