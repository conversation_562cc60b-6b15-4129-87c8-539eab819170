package com.socialplay.gpark.debug

import android.app.Activity
import androidx.fragment.app.FragmentManager
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.function.navigation.NavigationStateRecovery
import timber.log.Timber

/**
 * Navigation调试助手
 * 
 * 提供各种调试和诊断Navigation状态问题的工具
 */
object NavigationDebugHelper {
    
    private const val TAG = "NavigationDebugHelper"
    
    /**
     * 打印完整的Navigation状态信息
     */
    fun printFullNavigationState(activity: Activity) {
        if (!BuildConfig.DEBUG) return
        
        try {
            val navHostFragment = activity.supportFragmentManager
                .findFragmentById(com.socialplay.gpark.R.id.nav_host_fragment) as? NavHostFragment
            
            if (navHostFragment == null) {
                Timber.tag(TAG).w("NavHostFragment not found")
                return
            }
            
            val navController = navHostFragment.navController
            val fragmentManager = navHostFragment.childFragmentManager
            
            val report = buildString {
                appendLine("=== Navigation完整状态报告 ===")
                appendLine("时间: ${System.currentTimeMillis()}")
                appendLine()
                
                // Navigation状态
                appendLine("--- Navigation状态 ---")
                appendLine("当前目标: ${navController.currentDestination?.displayName ?: "null"}")
                appendLine("当前目标ID: ${navController.currentDestination?.id}")
                appendLine("Navigation栈大小: ${navController.backQueue.size}")
                appendLine("Navigation栈内容:")
                navController.backQueue.forEachIndexed { index, entry ->
                    appendLine("  [$index] ${entry.destination.displayName} (${entry.id})")
                }
                appendLine()
                
                // Fragment状态
                appendLine("--- Fragment状态 ---")
                appendLine("主导航Fragment: ${fragmentManager.primaryNavigationFragment?.javaClass?.simpleName ?: "null"}")
                appendLine("Fragment回退栈大小: ${fragmentManager.backStackEntryCount}")
                appendLine("活跃Fragment数量: ${fragmentManager.fragments.filter { !it.isRemoving }.size}")
                appendLine("Fragment回退栈内容:")
                for (i in 0 until fragmentManager.backStackEntryCount) {
                    val entry = fragmentManager.getBackStackEntryAt(i)
                    appendLine("  [$i] ${entry.name} (${entry.id})")
                }
                appendLine("活跃Fragment列表:")
                fragmentManager.fragments.filter { !it.isRemoving }.forEachIndexed { index, fragment ->
                    appendLine("  [$index] ${fragment.javaClass.simpleName} (${fragment.tag}) - ${fragment.lifecycle.currentState}")
                }
                appendLine()
                
                // 状态一致性检查
                appendLine("--- 状态一致性检查 ---")
                val stateSummary = NavigationStateRecovery.getNavigationStateSummary(navController, fragmentManager)
                appendLine(stateSummary)
                
                appendLine("=== 报告结束 ===")
            }
            
            Timber.tag(TAG).d(report)
            
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "打印Navigation状态失败")
        }
    }
    
    /**
     * 检查并报告Navigation状态问题
     */
    fun diagnoseNavigationIssues(activity: Activity): List<String> {
        val issues = mutableListOf<String>()
        
        try {
            val navHostFragment = activity.supportFragmentManager
                .findFragmentById(com.socialplay.gpark.R.id.nav_host_fragment) as? NavHostFragment
            
            if (navHostFragment == null) {
                issues.add("NavHostFragment未找到")
                return issues
            }
            
            val navController = navHostFragment.navController
            val fragmentManager = navHostFragment.childFragmentManager
            
            // 检查1: Navigation栈和Fragment栈大小差异
            val navStackSize = navController.backQueue.size
            val fragmentStackSize = fragmentManager.backStackEntryCount
            if (kotlin.math.abs(navStackSize - fragmentStackSize) > 2) {
                issues.add("Navigation栈($navStackSize)和Fragment栈($fragmentStackSize)大小差异过大")
            }
            
            // 检查2: 主导航Fragment与当前目标不匹配
            val currentDestination = navController.currentDestination?.displayName ?: ""
            val primaryFragment = fragmentManager.primaryNavigationFragment?.javaClass?.simpleName ?: ""
            
            if (currentDestination.contains("main", ignoreCase = true) && 
                !primaryFragment.contains("MainFragment", ignoreCase = true)) {
                issues.add("Navigation显示在main页面，但主Fragment不是MainFragment")
            }
            
            // 检查3: 孤儿Fragment
            val activeFragments = fragmentManager.fragments.filter { !it.isRemoving }
            val orphanFragments = activeFragments.filter { fragment ->
                val tag = fragment.tag
                tag != null && !navController.backQueue.any { it.id == tag }
            }
            
            if (orphanFragments.isNotEmpty()) {
                issues.add("发现${orphanFragments.size}个孤儿Fragment: ${orphanFragments.map { it.javaClass.simpleName }}")
            }
            
            // 检查4: 空的回退栈但有活跃Fragment
            if (fragmentStackSize == 0 && activeFragments.size > 1) {
                issues.add("Fragment回退栈为空但有${activeFragments.size}个活跃Fragment")
            }
            
            // 检查5: Navigation栈为空但有Fragment
            if (navStackSize <= 1 && fragmentStackSize > 0) {
                issues.add("Navigation栈几乎为空($navStackSize)但Fragment栈有内容($fragmentStackSize)")
            }
            
        } catch (e: Exception) {
            issues.add("诊断过程中发生异常: ${e.message}")
        }
        
        return issues
    }
    
    /**
     * 尝试自动修复Navigation状态问题
     */
    fun attemptAutoFix(activity: Activity): Boolean {
        try {
            val navHostFragment = activity.supportFragmentManager
                .findFragmentById(com.socialplay.gpark.R.id.nav_host_fragment) as? NavHostFragment
                ?: return false
            
            val navController = navHostFragment.navController
            val fragmentManager = navHostFragment.childFragmentManager
            
            // 使用状态恢复工具尝试修复
            val fixed = NavigationStateRecovery.checkAndFixStateInconsistency(navController, fragmentManager)
            
            if (fixed) {
                Timber.tag(TAG).d("自动修复已尝试")
            } else {
                Timber.tag(TAG).d("未检测到需要修复的问题或修复失败")
            }
            
            return fixed
            
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "自动修复失败")
            return false
        }
    }
    
    /**
     * 生成Navigation状态报告（用于bug报告）
     */
    fun generateBugReport(activity: Activity): String {
        return try {
            val issues = diagnoseNavigationIssues(activity)
            val timestamp = System.currentTimeMillis()
            
            buildString {
                appendLine("=== Navigation Bug Report ===")
                appendLine("时间戳: $timestamp")
                appendLine("发现的问题数量: ${issues.size}")
                appendLine()
                
                if (issues.isNotEmpty()) {
                    appendLine("问题列表:")
                    issues.forEachIndexed { index, issue ->
                        appendLine("${index + 1}. $issue")
                    }
                } else {
                    appendLine("未发现明显问题")
                }
                
                appendLine()
                appendLine("详细状态信息:")
                
                val navHostFragment = activity.supportFragmentManager
                    .findFragmentById(com.socialplay.gpark.R.id.nav_host_fragment) as? NavHostFragment
                
                if (navHostFragment != null) {
                    val navController = navHostFragment.navController
                    val fragmentManager = navHostFragment.childFragmentManager
                    
                    appendLine("Navigation当前: ${navController.currentDestination?.displayName}")
                    appendLine("Navigation栈: ${navController.backQueue.size}个条目")
                    appendLine("Fragment当前: ${fragmentManager.primaryNavigationFragment?.javaClass?.simpleName}")
                    appendLine("Fragment栈: ${fragmentManager.backStackEntryCount}个条目")
                    appendLine("活跃Fragment: ${fragmentManager.fragments.filter { !it.isRemoving }.size}个")
                }
                
                appendLine("=== Report End ===")
            }
        } catch (e: Exception) {
            "Bug报告生成失败: ${e.message}"
        }
    }
    
    /**
     * 快速状态检查（用于日常调试）
     */
    fun quickStatusCheck(activity: Activity): String {
        return try {
            val navHostFragment = activity.supportFragmentManager
                .findFragmentById(com.socialplay.gpark.R.id.nav_host_fragment) as? NavHostFragment
                ?: return "NavHostFragment未找到"
            
            val navController = navHostFragment.navController
            val fragmentManager = navHostFragment.childFragmentManager
            val issues = diagnoseNavigationIssues(activity)
            
            "Nav:${navController.currentDestination?.displayName?.substringAfterLast('.')} " +
            "Frag:${fragmentManager.primaryNavigationFragment?.javaClass?.simpleName} " +
            "NavStack:${navController.backQueue.size} " +
            "FragStack:${fragmentManager.backStackEntryCount} " +
            "Issues:${issues.size}"
            
        } catch (e: Exception) {
            "状态检查失败: ${e.message}"
        }
    }
}
