package com.socialplay.gpark.debug

import android.content.ComponentCallbacks2
import android.graphics.Bitmap
import android.graphics.Color
import com.socialplay.gpark.util.BitmapRecycleUtil
import com.socialplay.gpark.util.GlideCrashFix
import com.socialplay.gpark.util.GlideRecycledBitmapFix
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * Glide崩溃修复测试工具
 * 
 * 用于测试修复后的Glide是否能正确处理内存压力和Bitmap回收
 */
object GlideCrashFixTest {
    
    private const val TAG = "GlideCrashFixTest"
    
    /**
     * 测试Bitmap安全回收
     */
    fun testBitmapSafeRecycle() {
        Timber.tag(TAG).d("Starting bitmap safe recycle test...")
        
        GlobalScope.launch(Dispatchers.IO) {
            try {
                // 创建一些测试Bitmap
                val bitmaps = mutableListOf<Bitmap>()
                
                repeat(10) { i ->
                    val bitmap = Bitmap.createBitmap(100, 100, Bitmap.Config.ARGB_8888)
                    bitmap.eraseColor(Color.RED)
                    bitmaps.add(bitmap)
                    Timber.tag(TAG).d("Created test bitmap $i: ${bitmap.width}x${bitmap.height}")
                }
                
                // 测试安全回收
                bitmaps.forEachIndexed { index, bitmap ->
                    BitmapRecycleUtil.safeRecycle(bitmap)
                    Timber.tag(TAG).d("Safely recycled bitmap $index")
                }
                
                // 测试重复回收（应该不会崩溃）
                bitmaps.forEach { bitmap ->
                    BitmapRecycleUtil.safeRecycle(bitmap)
                }
                
                Timber.tag(TAG).d("Bitmap safe recycle test completed successfully")
                
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "Bitmap safe recycle test failed")
            }
        }
    }
    
    /**
     * 测试内存压力处理
     */
    fun testMemoryPressureHandling() {
        Timber.tag(TAG).d("Starting memory pressure handling test...")
        
        GlobalScope.launch(Dispatchers.Main) {
            try {
                // 模拟不同级别的内存压力
                val memoryLevels = listOf(
                    ComponentCallbacks2.TRIM_MEMORY_UI_HIDDEN,
                    ComponentCallbacks2.TRIM_MEMORY_BACKGROUND,
                    ComponentCallbacks2.TRIM_MEMORY_MODERATE,
                    ComponentCallbacks2.TRIM_MEMORY_COMPLETE
                )
                
                memoryLevels.forEach { level ->
                    Timber.tag(TAG).d("Testing memory pressure level: $level")
                    Timber.tag(TAG).d("BitmapPool status before: ${GlideCrashFix.getBitmapPoolStatus()}")
                    
                    GlideCrashFix.safeTrimGlideMemory(level)
                    
                    Timber.tag(TAG).d("BitmapPool status after: ${GlideCrashFix.getBitmapPoolStatus()}")
                }
                
                // 测试清理内存缓存
                Timber.tag(TAG).d("Testing memory cache clear...")
                GlideCrashFix.safeClearGlideMemory()
                
                Timber.tag(TAG).d("Memory pressure handling test completed successfully")
                
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "Memory pressure handling test failed")
            }
        }
    }
    
    /**
     * 测试BitmapPool状态检查
     */
    fun testBitmapPoolStatusCheck() {
        Timber.tag(TAG).d("Starting BitmapPool status check test...")
        
        try {
            // 创建一些不同大小的Bitmap进行测试
            val testCases = listOf(
                Triple(100, 100, "Normal size"),
                Triple(0, 0, "Zero size"),
                Triple(-1, -1, "Negative size"),
                Triple(5000, 5000, "Large size")
            )
            
            testCases.forEach { (width, height, description) ->
                val bitmap = if (width > 0 && height > 0) {
                    Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
                } else {
                    null
                }
                
                val canPut = GlideCrashFix.canSafelyPutToBitmapPool(bitmap)
                Timber.tag(TAG).d("$description (${width}x${height}): canPut = $canPut")
                
                bitmap?.let { BitmapRecycleUtil.safeRecycle(it) }
            }
            
            // 测试已回收的Bitmap
            val recycledBitmap = Bitmap.createBitmap(50, 50, Bitmap.Config.ARGB_8888)
            recycledBitmap.recycle()
            val canPutRecycled = GlideCrashFix.canSafelyPutToBitmapPool(recycledBitmap)
            Timber.tag(TAG).d("Recycled bitmap: canPut = $canPutRecycled")
            
            Timber.tag(TAG).d("BitmapPool status check test completed successfully")
            
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "BitmapPool status check test failed")
        }
    }
    
    /**
     * 测试Glide回收Bitmap修复
     */
    fun testGlideRecycledBitmapFix() {
        Timber.tag(TAG).d("Starting Glide recycled bitmap fix test...")

        try {
            // 检查修复是否已应用
            val isApplied = GlideRecycledBitmapFix.isFixApplied()
            Timber.tag(TAG).d("Glide recycled bitmap fix applied: $isApplied")

            // 获取BitmapPool信息
            val poolInfo = GlideRecycledBitmapFix.getBitmapPoolInfo()
            Timber.tag(TAG).d("BitmapPool info: $poolInfo")

            // 如果没有应用修复，尝试应用
            if (!isApplied) {
                GlideRecycledBitmapFix.applyFix()
                Timber.tag(TAG).d("Applied Glide recycled bitmap fix")
            }

            Timber.tag(TAG).d("Glide recycled bitmap fix test completed")

        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Glide recycled bitmap fix test failed")
        }
    }

    /**
     * 运行所有测试
     */
    fun runAllTests() {
        Timber.tag(TAG).d("Starting all Glide crash fix tests...")

        testBitmapSafeRecycle()
        testMemoryPressureHandling()
        testBitmapPoolStatusCheck()
        testGlideRecycledBitmapFix()

        Timber.tag(TAG).d("All tests initiated")
    }
}
